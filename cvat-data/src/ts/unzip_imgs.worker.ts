// Copyright (C) 2021-2022 Intel Corporation
// Copyright (C) CVAT.ai Corporation
//
// SPDX-License-Identifier: MIT

import J<PERSON><PERSON><PERSON> from 'jszip';

onmessage = (e) => {
    let errored = false;
    function handleError(error): void {
        try {
            if (!errored) {
                postMessage({ error });
            }
        } finally {
            errored = true;
        }
    }

    try {
        if (e.data) {
            const {
                start, end, block, dimension, dimension2D,
            } = e.data;

            const blob = new Blob([block], { type: 'application/octet-stream' });
            if (dimension === dimension2D) {
                createImageBitmap(blob).then((img) => {
                    postMessage({
                        fileName: '123.jpeg',
                        index: 0,
                        data: img,
                    });
                });
            } else {
                postMessage({
                    fileName: '123.jpeg',
                    index: 0,
                    data: blob,
                });
            }
        }
    } catch (error) {
        handleError(error);
    }
};

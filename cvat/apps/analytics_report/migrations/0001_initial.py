# Generated by Django 4.2.1 on 2023-07-05 09:08

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("engine", "0072_alter_issue_updated_date"),
    ]

    operations = [
        migrations.CreateModel(
            name="AnalyticsReport",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_date", models.DateTimeField(auto_now=True)),
                ("statistics", models.JSONField()),
                (
                    "job",
                    models.OneToOneField(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="analytics_report",
                        to="engine.job",
                    ),
                ),
                (
                    "project",
                    models.OneToOneField(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="analytics_report",
                        to="engine.project",
                    ),
                ),
                (
                    "task",
                    models.OneToOneField(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="analytics_report",
                        to="engine.task",
                    ),
                ),
            ],
        ),
    ]

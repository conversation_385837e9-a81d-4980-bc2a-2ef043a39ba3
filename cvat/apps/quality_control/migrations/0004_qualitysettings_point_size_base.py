# Generated by Django 4.2.15 on 2024-11-06 15:39

from django.db import migrations, models

import cvat.apps.quality_control.models


class Migration(migrations.Migration):

    dependencies = [
        ("quality_control", "0003_qualityreport_assignee_last_updated_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="qualitysettings",
            name="point_size_base",
            field=models.CharField(
                choices=[("image_size", "IMAGE_SIZE"), ("group_bbox_size", "GROUP_BBOX_SIZE")],
                default=cvat.apps.quality_control.models.PointSizeBase["GROUP_BBOX_SIZE"],
                max_length=32,
            ),
        ),
    ]

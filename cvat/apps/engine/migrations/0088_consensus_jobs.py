# Generated by Django 4.2.17 on 2025-01-13 15:51

import django.db.models.deletion
from django.db import migrations, models

import cvat.apps.engine.models


class Migration(migrations.Migration):

    dependencies = [
        ("engine", "0087_alter_label_type"),
    ]

    operations = [
        migrations.AddField(
            model_name="job",
            name="parent_job",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="child_jobs",
                related_query_name="child_job",
                to="engine.job",
            ),
        ),
        migrations.AddField(
            model_name="task",
            name="consensus_replicas",
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="job",
            name="type",
            field=models.CharField(
                choices=[
                    ("annotation", "ANNOTATION"),
                    ("ground_truth", "GROUND_TRUTH"),
                    ("consensus_replica", "CONSENSUS_REPLICA"),
                ],
                default=cvat.apps.engine.models.JobType["ANNOTATION"],
                max_length=32,
            ),
        ),
    ]

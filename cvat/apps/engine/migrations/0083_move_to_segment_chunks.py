# Generated by Django 4.2.13 on 2024-08-12 09:49

import os
from collections.abc import Iterable
from itertools import islice
from typing import TypeVar

from django.db import migrations

from cvat.apps.engine.log import get_migration_log_dir, get_migration_logger

T = TypeVar("T")


def take_by(iterable: Iterable[T], count: int) -> Iterable[T]:
    """
    Returns elements from the input iterable by batches of N items.
    ('abcdefg', 3) -> ['a', 'b', 'c'], ['d', 'e', 'f'], ['g']
    """

    it = iter(iterable)
    while True:
        batch = list(islice(it, count))
        if len(batch) == 0:
            break

        yield batch


def get_migration_name() -> str:
    return os.path.splitext(os.path.basename(__file__))[0]


def get_updated_ids_filename(log_dir: str, migration_name: str) -> str:
    return os.path.join(log_dir, migration_name + "-data_ids.log")


MIGRATION_LOG_HEADER = (
    'The following Data ids have been switched from using "filesystem" chunk storage ' 'to "cache":'
)


def switch_tasks_with_static_chunks_to_dynamic_chunks(apps, schema_editor):
    migration_name = get_migration_name()
    migration_log_dir = get_migration_log_dir()
    with get_migration_logger(migration_name) as common_logger:
        Data = apps.get_model("engine", "Data")

        data_with_static_cache_query = Data.objects.filter(storage_method="file_system")

        data_with_static_cache_ids = list(
            v[0]
            for v in (
                data_with_static_cache_query.order_by("id")
                .values_list("id")
                .iterator(chunk_size=100000)
            )
        )

        data_with_static_cache_query.update(storage_method="cache")

        updated_ids_filename = get_updated_ids_filename(migration_log_dir, migration_name)
        with open(updated_ids_filename, "w") as data_ids_file:
            print(MIGRATION_LOG_HEADER, file=data_ids_file)

            for data_id in data_with_static_cache_ids:
                print(data_id, file=data_ids_file)

        common_logger.info(
            "Information about migrated tasks is available in the migration log file: "
            "{}. You will need to remove data manually for these tasks.".format(
                updated_ids_filename
            )
        )


def revert_switch_tasks_with_static_chunks_to_dynamic_chunks(apps, schema_editor):
    migration_name = get_migration_name()
    migration_log_dir = get_migration_log_dir()

    updated_ids_filename = get_updated_ids_filename(migration_log_dir, migration_name)
    if not os.path.isfile(updated_ids_filename):
        raise FileNotFoundError(
            "Can't revert the migration: can't file forward migration logfile at "
            f"'{updated_ids_filename}'."
        )

    with open(updated_ids_filename, "r") as data_ids_file:
        header = data_ids_file.readline().strip()
        if header != MIGRATION_LOG_HEADER:
            raise ValueError(
                "Can't revert the migration: the migration log file has unexpected header"
            )

        forward_updated_ids = tuple(map(int, data_ids_file))

    if not forward_updated_ids:
        return

    Data = apps.get_model("engine", "Data")

    for id_batch in take_by(forward_updated_ids, 1000):
        Data.objects.filter(storage_method="cache", id__in=id_batch).update(
            storage_method="file_system"
        )


class Migration(migrations.Migration):

    dependencies = [
        ("engine", "0082_alter_labeledimage_job_and_more"),
    ]

    operations = [
        migrations.RunPython(
            switch_tasks_with_static_chunks_to_dynamic_chunks,
            reverse_code=revert_switch_tasks_with_static_chunks_to_dynamic_chunks,
        )
    ]

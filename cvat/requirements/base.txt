# SHA1:9c45ee6ba604552349bcaf41a8f35abbc7c62ddd
#
# This file is autogenerated by pip-compile-multi
# To update, run:
#
#    pip-compile-multi
#
-r ../../utils/dataset_manifest/requirements.txt
asgiref==3.8.1
    # via django
async-timeout==5.0.1
    # via redis
attrs==21.4.0
    # via
    #   -r cvat/requirements/base.in
    #   datumaro
    #   jsonschema
azure-core==1.32.0
    # via
    #   azure-storage-blob
    #   msrest
azure-storage-blob==12.13.0
    # via -r cvat/requirements/base.in
boto3==1.17.61
    # via -r cvat/requirements/base.in
botocore==1.20.112
    # via
    #   boto3
    #   s3transfer
cachetools==5.5.1
    # via google-auth
certifi==2024.12.14
    # via
    #   clickhouse-connect
    #   msrest
    #   requests
cffi==1.17.1
    # via cryptography
charset-normalizer==3.4.1
    # via requests
click==8.1.8
    # via
    #   nltk
    #   rq
clickhouse-connect==0.6.8
    # via -r cvat/requirements/base.in
contourpy==1.2.1
    # via matplotlib
coreapi==2.3.3
    # via -r cvat/requirements/base.in
coreschema==0.0.4
    # via coreapi
crontab==1.0.1
    # via rq-scheduler
cryptography==44.0.0
    # via
    #   azure-storage-blob
    #   datumaro
    #   pyjwt
cycler==0.12.1
    # via matplotlib
datumaro @ git+https://github.com/cvat-ai/datumaro.git@e61ebe4c5cfee8a741fbf89f966535996be6dcff
    # via -r cvat/requirements/base.in
defusedxml==0.7.1
    # via
    #   datumaro
    #   python3-openid
deprecated==1.2.18
    # via limits
dj-pagination==2.5.0
    # via -r cvat/requirements/base.in
dj-rest-auth[with-social]==5.0.2
    # via -r cvat/requirements/base.in
django==4.2.18
    # via
    #   -r cvat/requirements/base.in
    #   dj-rest-auth
    #   django-allauth
    #   django-appconf
    #   django-auth-ldap
    #   django-cors-headers
    #   django-crum
    #   django-filter
    #   django-health-check
    #   django-rq
    #   django-sendfile2
    #   djangorestframework
    #   drf-spectacular
django-allauth[saml]==0.57.2
    # via
    #   -r cvat/requirements/base.in
    #   dj-rest-auth
django-appconf==1.0.6
    # via django-compressor
django-auth-ldap==2.2.0
    # via -r cvat/requirements/base.in
django-compressor==4.3.1
    # via -r cvat/requirements/base.in
django-cors-headers==3.5.0
    # via -r cvat/requirements/base.in
django-crum==0.7.9
    # via -r cvat/requirements/base.in
django-filter==2.4.0
    # via -r cvat/requirements/base.in
django-health-check==3.18.3
    # via -r cvat/requirements/base.in
django-rq==2.8.1
    # via -r cvat/requirements/base.in
django-sendfile2==0.7.0
    # via -r cvat/requirements/base.in
djangorestframework==3.15.2
    # via
    #   -r cvat/requirements/base.in
    #   dj-rest-auth
    #   drf-spectacular
drf-spectacular==0.26.2
    # via -r cvat/requirements/base.in
easyprocess==1.1
    # via pyunpack
entrypoint2==1.1
    # via pyunpack
fonttools==4.55.8
    # via matplotlib
freezegun==1.5.1
    # via rq-scheduler
furl==2.1.0
    # via -r cvat/requirements/base.in
google-api-core==2.24.1
    # via
    #   google-cloud-core
    #   google-cloud-storage
google-auth==2.38.0
    # via
    #   google-api-core
    #   google-cloud-core
    #   google-cloud-storage
google-cloud-core==2.4.1
    # via google-cloud-storage
google-cloud-storage==1.42.0
    # via -r cvat/requirements/base.in
google-crc32c==1.6.0
    # via google-resumable-media
google-resumable-media==2.7.2
    # via google-cloud-storage
googleapis-common-protos==1.66.0
    # via google-api-core
h5py==3.12.1
    # via datumaro
idna==3.10
    # via requests
imagesize==1.4.1
    # via datumaro
importlib-metadata==8.6.1
    # via clickhouse-connect
importlib-resources==6.5.2
    # via
    #   matplotlib
    #   nibabel
inflection==0.5.1
    # via drf-spectacular
isodate==0.7.2
    # via
    #   msrest
    #   python3-saml
itypes==1.2.0
    # via coreapi
jinja2==3.1.5
    # via coreschema
jmespath==0.10.0
    # via
    #   boto3
    #   botocore
joblib==1.4.2
    # via
    #   nltk
    #   scikit-learn
json-stream==2.3.3
    # via datumaro
json-stream-rs-tokenizer==0.4.27
    # via json-stream
jsonschema==4.17.3
    # via drf-spectacular
kiwisolver==1.4.7
    # via matplotlib
limits==4.0.1
    # via python-logstash-async
lxml==5.3.0
    # via
    #   -r cvat/requirements/base.in
    #   datumaro
    #   python3-saml
    #   xmlsec
lz4==4.4.3
    # via clickhouse-connect
markupsafe==3.0.2
    # via jinja2
matplotlib==3.8.4
    # via
    #   datumaro
    #   pycocotools
mmh3==5.1.0
    # via pottery
msrest==0.7.1
    # via azure-storage-blob
networkx==3.2.1
    # via datumaro
nibabel==5.3.2
    # via datumaro
nltk==3.9.1
    # via datumaro
oauthlib==3.2.2
    # via requests-oauthlib
orderedmultidict==1.0.1
    # via furl
orjson==3.10.12
    # via datumaro
packaging==24.2
    # via
    #   limits
    #   matplotlib
    #   nibabel
    #   tensorboardx
pandas==2.2.3
    # via datumaro
patool==1.12
    # via -r cvat/requirements/base.in
pdf2image==1.14.0
    # via -r cvat/requirements/base.in
portalocker==3.1.1
    # via datumaro
pottery==3.0.0
    # via -r cvat/requirements/base.in
proto-plus==1.26.0
    # via google-api-core
protobuf==5.29.3
    # via
    #   datumaro
    #   google-api-core
    #   googleapis-common-protos
    #   proto-plus
    #   tensorboardx
psutil==5.9.4
    # via -r cvat/requirements/base.in
psycopg2-binary==2.9.5
    # via -r cvat/requirements/base.in
pyarrow==19.0.0
    # via datumaro
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   python-ldap
    #   rsa
pyasn1-modules==0.4.1
    # via
    #   google-auth
    #   python-ldap
pycocotools==2.0.8
    # via datumaro
pycparser==2.22
    # via cffi
pyemd==1.0.0
    # via datumaro
pyjwt[crypto]==2.10.1
    # via django-allauth
pylogbeat==2.0.1
    # via python-logstash-async
pyparsing==3.2.1
    # via matplotlib
pyrsistent==0.20.0
    # via jsonschema
python-dateutil==2.9.0.post0
    # via
    #   botocore
    #   freezegun
    #   matplotlib
    #   pandas
    #   rq-scheduler
python-ldap==3.4.3
    # via
    #   -r cvat/requirements/base.in
    #   django-auth-ldap
python-logstash-async==2.5.0
    # via -r cvat/requirements/base.in
python3-openid==3.2.0
    # via django-allauth
python3-saml==1.16.0
    # via django-allauth
pytz==2024.2
    # via
    #   clickhouse-connect
    #   pandas
pyunpack==0.2.1
    # via -r cvat/requirements/base.in
pyyaml==6.0.2
    # via
    #   datumaro
    #   drf-spectacular
rcssmin==1.1.1
    # via django-compressor
redis==4.6.0
    # via
    #   -r cvat/requirements/base.in
    #   django-rq
    #   pottery
    #   rq
regex==2024.11.6
    # via nltk
requests==2.32.3
    # via
    #   -r cvat/requirements/base.in
    #   azure-core
    #   coreapi
    #   datumaro
    #   django-allauth
    #   google-api-core
    #   google-cloud-storage
    #   msrest
    #   python-logstash-async
    #   requests-oauthlib
requests-oauthlib==2.0.0
    # via
    #   django-allauth
    #   msrest
rjsmin==1.2.1
    # via django-compressor
rq==1.16.0
    # via
    #   -r cvat/requirements/base.in
    #   django-rq
    #   rq-scheduler
rq-scheduler==0.13.1
    # via -r cvat/requirements/base.in
rsa==4.9
    # via google-auth
ruamel-yaml==0.18.10
    # via datumaro
ruamel-yaml-clib==0.2.12
    # via ruamel-yaml
rules==3.5
    # via -r cvat/requirements/base.in
s3transfer==0.4.2
    # via boto3
scikit-learn==1.6.1
    # via datumaro
scipy==1.13.1
    # via
    #   datumaro
    #   scikit-learn
shapely==1.7.1
    # via
    #   -r cvat/requirements/base.in
    #   datumaro
six==1.17.0
    # via
    #   azure-core
    #   furl
    #   orderedmultidict
    #   python-dateutil
sqlparse==0.5.3
    # via django
tabulate==0.9.0
    # via datumaro
tensorboardx==*******
    # via datumaro
threadpoolctl==3.5.0
    # via scikit-learn
typing-extensions==4.12.2
    # via
    #   asgiref
    #   azure-core
    #   datumaro
    #   limits
    #   nibabel
    #   pottery
tzdata==2025.1
    # via pandas
uritemplate==4.1.1
    # via
    #   coreapi
    #   drf-spectacular
urllib3==1.26.20
    # via
    #   botocore
    #   clickhouse-connect
    #   requests
wrapt==1.17.2
    # via deprecated
xmlsec==1.3.14
    # via
    #   -r cvat/requirements/base.in
    #   python3-saml
zipp==3.21.0
    # via
    #   importlib-metadata
    #   importlib-resources
zstandard==0.23.0
    # via clickhouse-connect

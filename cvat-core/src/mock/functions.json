[{"id": "onnx-wongkinyiu-yolov7", "kind": "detector", "labels_v2": [{"name": "person", "type": "rectangle", "attributes": []}, {"name": "bicycle", "type": "rectangle", "attributes": []}, {"name": "car", "type": "rectangle", "attributes": []}, {"name": "motorbike", "type": "rectangle", "attributes": []}, {"name": "aeroplane", "type": "rectangle", "attributes": []}, {"name": "bus", "type": "rectangle", "attributes": []}, {"name": "train", "type": "rectangle", "attributes": []}, {"name": "truck", "type": "rectangle", "attributes": []}, {"name": "boat", "type": "rectangle", "attributes": []}, {"name": "traffic light", "type": "rectangle", "attributes": []}, {"name": "fire hydrant", "type": "rectangle", "attributes": []}, {"name": "stop sign", "type": "rectangle", "attributes": []}, {"name": "parking meter", "type": "rectangle", "attributes": []}, {"name": "bench", "type": "rectangle", "attributes": []}, {"name": "bird", "type": "rectangle", "attributes": []}, {"name": "cat", "type": "rectangle", "attributes": []}, {"name": "dog", "type": "rectangle", "attributes": []}, {"name": "horse", "type": "rectangle", "attributes": []}, {"name": "sheep", "type": "rectangle", "attributes": []}, {"name": "cow", "type": "rectangle", "attributes": []}, {"name": "elephant", "type": "rectangle", "attributes": []}, {"name": "bear", "type": "rectangle", "attributes": []}, {"name": "zebra", "type": "rectangle", "attributes": []}, {"name": "giraffe", "type": "rectangle", "attributes": []}, {"name": "backpack", "type": "rectangle", "attributes": []}, {"name": "umbrella", "type": "rectangle", "attributes": []}, {"name": "handbag", "type": "rectangle", "attributes": []}, {"name": "tie", "type": "rectangle", "attributes": []}, {"name": "suitcase", "type": "rectangle", "attributes": []}, {"name": "frisbee", "type": "rectangle", "attributes": []}, {"name": "skis", "type": "rectangle", "attributes": []}, {"name": "snowboard", "type": "rectangle", "attributes": []}, {"name": "sports ball", "type": "rectangle", "attributes": []}, {"name": "kite", "type": "rectangle", "attributes": []}, {"name": "baseball bat", "type": "rectangle", "attributes": []}, {"name": "baseball glove", "type": "rectangle", "attributes": []}, {"name": "skateboard", "type": "rectangle", "attributes": []}, {"name": "surfboard", "type": "rectangle", "attributes": []}, {"name": "tennis racket", "type": "rectangle", "attributes": []}, {"name": "bottle", "type": "rectangle", "attributes": []}, {"name": "wine glass", "type": "rectangle", "attributes": []}, {"name": "cup", "type": "rectangle", "attributes": []}, {"name": "fork", "type": "rectangle", "attributes": []}, {"name": "knife", "type": "rectangle", "attributes": []}, {"name": "spoon", "type": "rectangle", "attributes": []}, {"name": "bowl", "type": "rectangle", "attributes": []}, {"name": "banana", "type": "rectangle", "attributes": []}, {"name": "apple", "type": "rectangle", "attributes": []}, {"name": "sandwich", "type": "rectangle", "attributes": []}, {"name": "orange", "type": "rectangle", "attributes": []}, {"name": "broccoli", "type": "rectangle", "attributes": []}, {"name": "carrot", "type": "rectangle", "attributes": []}, {"name": "hot dog", "type": "rectangle", "attributes": []}, {"name": "pizza", "type": "rectangle", "attributes": []}, {"name": "donut", "type": "rectangle", "attributes": []}, {"name": "cake", "type": "rectangle", "attributes": []}, {"name": "chair", "type": "rectangle", "attributes": []}, {"name": "sofa", "type": "rectangle", "attributes": []}, {"name": "pottedplant", "type": "rectangle", "attributes": []}, {"name": "bed", "type": "rectangle", "attributes": []}, {"name": "diningtable", "type": "rectangle", "attributes": []}, {"name": "toilet", "type": "rectangle", "attributes": []}, {"name": "tvmonitor", "type": "rectangle", "attributes": []}, {"name": "laptop", "type": "rectangle", "attributes": []}, {"name": "mouse", "type": "rectangle", "attributes": []}, {"name": "remote", "type": "rectangle", "attributes": []}, {"name": "keyboard", "type": "rectangle", "attributes": []}, {"name": "cell phone", "type": "rectangle", "attributes": []}, {"name": "microwave", "type": "rectangle", "attributes": []}, {"name": "oven", "type": "rectangle", "attributes": []}, {"name": "toaster", "type": "rectangle", "attributes": []}, {"name": "sink", "type": "rectangle", "attributes": []}, {"name": "refrigerator", "type": "rectangle", "attributes": []}, {"name": "book", "type": "rectangle", "attributes": []}, {"name": "clock", "type": "rectangle", "attributes": []}, {"name": "vase", "type": "rectangle", "attributes": []}, {"name": "scissors", "type": "rectangle", "attributes": []}, {"name": "teddy bear", "type": "rectangle", "attributes": []}, {"name": "hair drier", "type": "rectangle", "attributes": []}, {"name": "toothbrush", "type": "rectangle", "attributes": []}], "description": "YOLO v7 via onnx", "name": "YOLO v7", "version": 1}, {"id": "pth-dschoerk-transt", "kind": "tracker", "labels_v2": [], "description": "Fast Online Object Tracking and Segmentation", "name": "TransT", "version": 1}, {"id": "pth-facebookresearch-sam2-large", "kind": "interactor", "labels_v2": [], "description": "Interactive object segmentation with Segment-Anything 2.0", "name": "Segment Anything 2.0", "version": 2, "min_pos_points": 0, "min_neg_points": 0, "startswith_box": false, "startswith_box_optional": true, "help_message": "The interactor allows to get a mask of an object using at least one positive, and any negative points inside it", "animated_gif": "https://raw.githubusercontent.com/cvat-ai/cvat/develop/site/content/en/images/hrnet_example.gif"}, {"id": "pth-mmpose-hrnet32", "kind": "detector", "labels_v2": [{"name": "body", "type": "skeleton", "attributes": [], "sublabels": [{"name": "1", "type": "points", "attributes": []}, {"name": "2", "type": "points", "attributes": []}, {"name": "3", "type": "points", "attributes": []}, {"name": "4", "type": "points", "attributes": []}, {"name": "5", "type": "points", "attributes": []}, {"name": "6", "type": "points", "attributes": []}, {"name": "7", "type": "points", "attributes": []}, {"name": "8", "type": "points", "attributes": []}, {"name": "9", "type": "points", "attributes": []}, {"name": "10", "type": "points", "attributes": []}, {"name": "11", "type": "points", "attributes": []}, {"name": "12", "type": "points", "attributes": []}, {"name": "13", "type": "points", "attributes": []}, {"name": "14", "type": "points", "attributes": []}, {"name": "15", "type": "points", "attributes": []}, {"name": "16", "type": "points", "attributes": []}, {"name": "17", "type": "points", "attributes": []}], "svg": "<line x1=\"54.25419998168945\" y1=\"7.804621696472168\" x2=\"51.2289924621582\" y2=\"7.636554718017578\" data-type=\"edge\" data-node-from=\"4\" data-node-to=\"2\"></line>\n<line x1=\"47.195377349853516\" y1=\"7.636554718017578\" x2=\"44.170169830322266\" y2=\"7.804621696472168\" data-type=\"edge\" data-node-from=\"3\" data-node-to=\"5\"></line>\n<line x1=\"48.87604904174805\" y1=\"9.485294342041016\" x2=\"47.195377349853516\" y2=\"7.636554718017578\" data-type=\"edge\" data-node-from=\"1\" data-node-to=\"3\"></line>\n<line x1=\"48.87604904174805\" y1=\"9.485294342041016\" x2=\"51.2289924621582\" y2=\"7.636554718017578\" data-type=\"edge\" data-node-from=\"1\" data-node-to=\"2\"></line>\n<line x1=\"60.80882263183594\" y1=\"19.90546226501465\" x2=\"48.87604904174805\" y2=\"9.485294342041016\" data-type=\"edge\" data-node-from=\"6\" data-node-to=\"1\"></line>\n<line x1=\"63.83403396606445\" y1=\"34.023109436035156\" x2=\"60.80882263183594\" y2=\"19.90546226501465\" data-type=\"edge\" data-node-from=\"8\" data-node-to=\"6\"></line>\n<line x1=\"66.85924530029297\" y1=\"47.132354736328125\" x2=\"63.83403396606445\" y2=\"34.023109436035156\" data-type=\"edge\" data-node-from=\"10\" data-node-to=\"8\"></line>\n<line x1=\"58.119747161865234\" y1=\"71.16596984863281\" x2=\"57.78361511230469\" y2=\"87.97268676757812\" data-type=\"edge\" data-node-from=\"14\" data-node-to=\"16\"></line>\n<line x1=\"57.11134338378906\" y1=\"49.65336227416992\" x2=\"58.119747161865234\" y2=\"71.16596984863281\" data-type=\"edge\" data-node-from=\"12\" data-node-to=\"14\"></line>\n<line x1=\"48.87604904174805\" y1=\"9.485294342041016\" x2=\"57.11134338378906\" y2=\"49.65336227416992\" data-type=\"edge\" data-node-from=\"1\" data-node-to=\"12\"></line>\n<line x1=\"44.00210189819336\" y1=\"50.157562255859375\" x2=\"48.87604904174805\" y2=\"9.485294342041016\" data-type=\"edge\" data-node-from=\"13\" data-node-to=\"1\"></line>\n<line x1=\"44.338233947753906\" y1=\"70.6617660522461\" x2=\"44.00210189819336\" y2=\"50.157562255859375\" data-type=\"edge\" data-node-from=\"15\" data-node-to=\"13\"></line>\n<line x1=\"46.186973571777344\" y1=\"92.51050567626953\" x2=\"44.338233947753906\" y2=\"70.6617660522461\" data-type=\"edge\" data-node-from=\"17\" data-node-to=\"15\"></line>\n<line x1=\"35.93487548828125\" y1=\"34.35924530029297\" x2=\"33.918067932128906\" y2=\"47.46848678588867\" data-type=\"edge\" data-node-from=\"9\" data-node-to=\"11\"></line>\n<line x1=\"37.78361511230469\" y1=\"20.409664154052734\" x2=\"35.93487548828125\" y2=\"34.35924530029297\" data-type=\"edge\" data-node-from=\"7\" data-node-to=\"9\"></line>\n<line x1=\"48.87604904174805\" y1=\"9.485294342041016\" x2=\"37.78361511230469\" y2=\"20.409664154052734\" data-type=\"edge\" data-node-from=\"1\" data-node-to=\"7\"></line>\n<circle r=\"0.75\" cx=\"48.87604904174805\" cy=\"9.485294342041016\" data-type=\"element node\" data-element-id=\"1\" data-node-id=\"1\" data-label-name=\"1\"></circle>\n<circle r=\"0.75\" cx=\"51.2289924621582\" cy=\"7.636554718017578\" data-type=\"element node\" data-element-id=\"2\" data-node-id=\"2\" data-label-name=\"2\"></circle>\n<circle r=\"0.75\" cx=\"47.195377349853516\" cy=\"7.636554718017578\" data-type=\"element node\" data-element-id=\"3\" data-node-id=\"3\" data-label-name=\"3\"></circle>\n<circle r=\"0.75\" cx=\"54.25419998168945\" cy=\"7.804621696472168\" data-type=\"element node\" data-element-id=\"4\" data-node-id=\"4\" data-label-name=\"4\"></circle>\n<circle r=\"0.75\" cx=\"44.170169830322266\" cy=\"7.804621696472168\" data-type=\"element node\" data-element-id=\"5\" data-node-id=\"5\" data-label-name=\"5\"></circle>\n<circle r=\"0.75\" cx=\"60.80882263183594\" cy=\"19.90546226501465\" data-type=\"element node\" data-element-id=\"6\" data-node-id=\"6\" data-label-name=\"6\"></circle>\n<circle r=\"0.75\" cx=\"37.78361511230469\" cy=\"20.409664154052734\" data-type=\"element node\" data-element-id=\"7\" data-node-id=\"7\" data-label-name=\"7\"></circle>\n<circle r=\"0.75\" cx=\"63.83403396606445\" cy=\"34.023109436035156\" data-type=\"element node\" data-element-id=\"8\" data-node-id=\"8\" data-label-name=\"8\"></circle>\n<circle r=\"0.75\" cx=\"35.93487548828125\" cy=\"34.35924530029297\" data-type=\"element node\" data-element-id=\"9\" data-node-id=\"9\" data-label-name=\"9\"></circle>\n<circle r=\"0.75\" cx=\"66.85924530029297\" cy=\"47.132354736328125\" data-type=\"element node\" data-element-id=\"10\" data-node-id=\"10\" data-label-name=\"10\"></circle>\n<circle r=\"0.75\" cx=\"33.918067932128906\" cy=\"47.46848678588867\" data-type=\"element node\" data-element-id=\"11\" data-node-id=\"11\" data-label-name=\"11\"></circle>\n<circle r=\"0.75\" cx=\"57.11134338378906\" cy=\"49.65336227416992\" data-type=\"element node\" data-element-id=\"12\" data-node-id=\"12\" data-label-name=\"12\"></circle>\n<circle r=\"0.75\" cx=\"44.00210189819336\" cy=\"50.157562255859375\" data-type=\"element node\" data-element-id=\"13\" data-node-id=\"13\" data-label-name=\"13\"></circle>\n<circle r=\"0.75\" cx=\"58.119747161865234\" cy=\"71.16596984863281\" data-type=\"element node\" data-element-id=\"14\" data-node-id=\"14\" data-label-name=\"14\"></circle>\n<circle r=\"0.75\" cx=\"44.338233947753906\" cy=\"70.6617660522461\" data-type=\"element node\" data-element-id=\"15\" data-node-id=\"15\" data-label-name=\"15\"></circle>\n<circle r=\"0.75\" cx=\"57.78361511230469\" cy=\"87.97268676757812\" data-type=\"element node\" data-element-id=\"16\" data-node-id=\"16\" data-label-name=\"16\"></circle>\n<circle r=\"0.75\" cx=\"46.186973571777344\" cy=\"92.51050567626953\" data-type=\"element node\" data-element-id=\"17\" data-node-id=\"17\" data-label-name=\"17\"></circle>"}, {"name": "feet", "type": "skeleton", "attributes": [], "sublabels": [{"name": "1", "type": "points", "attributes": []}, {"name": "2", "type": "points", "attributes": []}, {"name": "3", "type": "points", "attributes": []}, {"name": "4", "type": "points", "attributes": []}, {"name": "5", "type": "points", "attributes": []}, {"name": "6", "type": "points", "attributes": []}], "svg": "<circle r=\"0.75\" cx=\"58.28781509399414\" cy=\"16.37605094909668\" data-type=\"element node\" data-element-id=\"1\" data-node-id=\"1\" data-label-name=\"1\"></circle>\n<circle r=\"0.75\" cx=\"81.48109436035156\" cy=\"26.796218872070312\" data-type=\"element node\" data-element-id=\"2\" data-node-id=\"2\" data-label-name=\"2\"></circle>\n<circle r=\"0.75\" cx=\"68.20378112792969\" cy=\"84.27520751953125\" data-type=\"element node\" data-element-id=\"3\" data-node-id=\"3\" data-label-name=\"3\"></circle>\n<circle r=\"0.75\" cx=\"41.313026428222656\" cy=\"16.71218490600586\" data-type=\"element node\" data-element-id=\"4\" data-node-id=\"4\" data-label-name=\"4\"></circle>\n<circle r=\"0.75\" cx=\"17.27941131591797\" cy=\"26.12394905090332\" data-type=\"element node\" data-element-id=\"5\" data-node-id=\"5\" data-label-name=\"5\"></circle>\n<circle r=\"0.75\" cx=\"30.724790573120117\" cy=\"85.11554718017578\" data-type=\"element node\" data-element-id=\"6\" data-node-id=\"6\" data-label-name=\"6\"></circle>"}, {"name": "face", "type": "skeleton", "attributes": [], "sublabels": [{"name": "1", "type": "points", "attributes": []}, {"name": "2", "type": "points", "attributes": []}, {"name": "3", "type": "points", "attributes": []}, {"name": "4", "type": "points", "attributes": []}, {"name": "5", "type": "points", "attributes": []}, {"name": "6", "type": "points", "attributes": []}, {"name": "7", "type": "points", "attributes": []}, {"name": "8", "type": "points", "attributes": []}, {"name": "9", "type": "points", "attributes": []}, {"name": "10", "type": "points", "attributes": []}, {"name": "11", "type": "points", "attributes": []}, {"name": "12", "type": "points", "attributes": []}, {"name": "13", "type": "points", "attributes": []}, {"name": "14", "type": "points", "attributes": []}, {"name": "15", "type": "points", "attributes": []}, {"name": "16", "type": "points", "attributes": []}, {"name": "17", "type": "points", "attributes": []}, {"name": "18", "type": "points", "attributes": []}, {"name": "19", "type": "points", "attributes": []}, {"name": "20", "type": "points", "attributes": []}, {"name": "21", "type": "points", "attributes": []}, {"name": "22", "type": "points", "attributes": []}, {"name": "23", "type": "points", "attributes": []}, {"name": "24", "type": "points", "attributes": []}, {"name": "25", "type": "points", "attributes": []}, {"name": "26", "type": "points", "attributes": []}, {"name": "27", "type": "points", "attributes": []}, {"name": "28", "type": "points", "attributes": []}, {"name": "29", "type": "points", "attributes": []}, {"name": "30", "type": "points", "attributes": []}, {"name": "31", "type": "points", "attributes": []}, {"name": "32", "type": "points", "attributes": []}, {"name": "33", "type": "points", "attributes": []}, {"name": "34", "type": "points", "attributes": []}, {"name": "35", "type": "points", "attributes": []}, {"name": "36", "type": "points", "attributes": []}, {"name": "37", "type": "points", "attributes": []}, {"name": "38", "type": "points", "attributes": []}, {"name": "39", "type": "points", "attributes": []}, {"name": "40", "type": "points", "attributes": []}, {"name": "41", "type": "points", "attributes": []}, {"name": "42", "type": "points", "attributes": []}, {"name": "43", "type": "points", "attributes": []}, {"name": "44", "type": "points", "attributes": []}, {"name": "45", "type": "points", "attributes": []}, {"name": "46", "type": "points", "attributes": []}, {"name": "47", "type": "points", "attributes": []}, {"name": "48", "type": "points", "attributes": []}, {"name": "49", "type": "points", "attributes": []}, {"name": "50", "type": "points", "attributes": []}, {"name": "51", "type": "points", "attributes": []}, {"name": "52", "type": "points", "attributes": []}, {"name": "53", "type": "points", "attributes": []}, {"name": "54", "type": "points", "attributes": []}, {"name": "55", "type": "points", "attributes": []}, {"name": "56", "type": "points", "attributes": []}, {"name": "57", "type": "points", "attributes": []}, {"name": "58", "type": "points", "attributes": []}, {"name": "59", "type": "points", "attributes": []}, {"name": "60", "type": "points", "attributes": []}, {"name": "61", "type": "points", "attributes": []}, {"name": "62", "type": "points", "attributes": []}, {"name": "63", "type": "points", "attributes": []}, {"name": "64", "type": "points", "attributes": []}, {"name": "65", "type": "points", "attributes": []}, {"name": "66", "type": "points", "attributes": []}, {"name": "67", "type": "points", "attributes": []}, {"name": "68", "type": "points", "attributes": []}], "svg": "<circle r=\"0.75\" cx=\"13.91806697845459\" cy=\"22.426469802856445\" data-type=\"element node\" data-element-id=\"1\" data-node-id=\"1\" data-label-name=\"1\"></circle>\n<circle r=\"0.75\" cx=\"15.262604713439941\" cy=\"34.023109436035156\" data-type=\"element node\" data-element-id=\"2\" data-node-id=\"2\" data-label-name=\"2\"></circle>\n<circle r=\"0.75\" cx=\"17.447479248046875\" cy=\"43.60293960571289\" data-type=\"element node\" data-element-id=\"3\" data-node-id=\"3\" data-label-name=\"3\"></circle>\n<circle r=\"0.75\" cx=\"19.128150939941406\" cy=\"53.85504150390625\" data-type=\"element node\" data-element-id=\"4\" data-node-id=\"4\" data-label-name=\"4\"></circle>\n<circle r=\"0.75\" cx=\"21.81722640991211\" cy=\"63.60293960571289\" data-type=\"element node\" data-element-id=\"5\" data-node-id=\"5\" data-label-name=\"5\"></circle>\n<circle r=\"0.75\" cx=\"27.531513214111328\" cy=\"72.67857360839844\" data-type=\"element node\" data-element-id=\"6\" data-node-id=\"6\" data-label-name=\"6\"></circle>\n<circle r=\"0.75\" cx=\"34.08613586425781\" cy=\"80.07353210449219\" data-type=\"element node\" data-element-id=\"7\" data-node-id=\"7\" data-label-name=\"7\"></circle>\n<circle r=\"0.75\" cx=\"43.329830169677734\" cy=\"87.46849060058594\" data-type=\"element node\" data-element-id=\"8\" data-node-id=\"8\" data-label-name=\"8\"></circle>\n<circle r=\"0.75\" cx=\"55.59873962402344\" cy=\"90.32563018798828\" data-type=\"element node\" data-element-id=\"9\" data-node-id=\"9\" data-label-name=\"9\"></circle>\n<circle r=\"0.75\" cx=\"65.85083770751953\" cy=\"87.80461883544922\" data-type=\"element node\" data-element-id=\"10\" data-node-id=\"10\" data-label-name=\"10\"></circle>\n<circle r=\"0.75\" cx=\"73.41386413574219\" cy=\"80.40966033935547\" data-type=\"element node\" data-element-id=\"11\" data-node-id=\"11\" data-label-name=\"11\"></circle>\n<circle r=\"0.75\" cx=\"79.46428680419922\" cy=\"73.18276977539062\" data-type=\"element node\" data-element-id=\"12\" data-node-id=\"12\" data-label-name=\"12\"></circle>\n<circle r=\"0.75\" cx=\"85.17857360839844\" cy=\"63.43487548828125\" data-type=\"element node\" data-element-id=\"13\" data-node-id=\"13\" data-label-name=\"13\"></circle>\n<circle r=\"0.75\" cx=\"87.19538116455078\" cy=\"52.67856979370117\" data-type=\"element node\" data-element-id=\"14\" data-node-id=\"14\" data-label-name=\"14\"></circle>\n<circle r=\"0.75\" cx=\"88.87605285644531\" cy=\"41.081932067871094\" data-type=\"element node\" data-element-id=\"15\" data-node-id=\"15\" data-label-name=\"15\"></circle>\n<circle r=\"0.75\" cx=\"90.38865661621094\" cy=\"32.00630187988281\" data-type=\"element node\" data-element-id=\"16\" data-node-id=\"16\" data-label-name=\"16\"></circle>\n<circle r=\"0.75\" cx=\"91.90126037597656\" cy=\"20.409664154052734\" data-type=\"element node\" data-element-id=\"17\" data-node-id=\"17\" data-label-name=\"17\"></circle>\n<circle r=\"0.75\" cx=\"19.296218872070312\" cy=\"16.71218490600586\" data-type=\"element node\" data-element-id=\"18\" data-node-id=\"18\" data-label-name=\"18\"></circle>\n<circle r=\"0.75\" cx=\"23.834033966064453\" cy=\"10.829832077026367\" data-type=\"element node\" data-element-id=\"19\" data-node-id=\"19\" data-label-name=\"19\"></circle>\n<circle r=\"0.75\" cx=\"29.88445472717285\" cy=\"9.485294342041016\" data-type=\"element node\" data-element-id=\"20\" data-node-id=\"20\" data-label-name=\"20\"></circle>\n<circle r=\"0.75\" cx=\"36.943275451660156\" cy=\"9.989496231079102\" data-type=\"element node\" data-element-id=\"21\" data-node-id=\"21\" data-label-name=\"21\"></circle>\n<circle r=\"0.75\" cx=\"44.170169830322266\" cy=\"13.51890754699707\" data-type=\"element node\" data-element-id=\"22\" data-node-id=\"22\" data-label-name=\"22\"></circle>\n<circle r=\"0.75\" cx=\"61.4810905456543\" cy=\"12.342436790466309\" data-type=\"element node\" data-element-id=\"23\" data-node-id=\"23\" data-label-name=\"23\"></circle>\n<circle r=\"0.75\" cx=\"66.85924530029297\" cy=\"9.317227363586426\" data-type=\"element node\" data-element-id=\"24\" data-node-id=\"24\" data-label-name=\"24\"></circle>\n<circle r=\"0.75\" cx=\"74.42227172851562\" cy=\"7.46848726272583\" data-type=\"element node\" data-element-id=\"25\" data-node-id=\"25\" data-label-name=\"25\"></circle>\n<circle r=\"0.75\" cx=\"80.30461883544922\" cy=\"7.804621696472168\" data-type=\"element node\" data-element-id=\"26\" data-node-id=\"26\" data-label-name=\"26\"></circle>\n<circle r=\"0.75\" cx=\"85.68276977539062\" cy=\"13.182772636413574\" data-type=\"element node\" data-element-id=\"27\" data-node-id=\"27\" data-label-name=\"27\"></circle>\n<circle r=\"0.75\" cx=\"52.909664154052734\" cy=\"20.91386604309082\" data-type=\"element node\" data-element-id=\"28\" data-node-id=\"28\" data-label-name=\"28\"></circle>\n<circle r=\"0.75\" cx=\"53.24580001831055\" cy=\"28.476890563964844\" data-type=\"element node\" data-element-id=\"29\" data-node-id=\"29\" data-label-name=\"29\"></circle>\n<circle r=\"0.75\" cx=\"53.918067932128906\" cy=\"35.367645263671875\" data-type=\"element node\" data-element-id=\"30\" data-node-id=\"30\" data-label-name=\"30\"></circle>\n<circle r=\"0.75\" cx=\"53.75\" cy=\"42.42647171020508\" data-type=\"element node\" data-element-id=\"31\" data-node-id=\"31\" data-label-name=\"31\"></circle>\n<circle r=\"0.75\" cx=\"44.00210189819336\" cy=\"47.97269058227539\" data-type=\"element node\" data-element-id=\"32\" data-node-id=\"32\" data-label-name=\"32\"></circle>\n<circle r=\"0.75\" cx=\"48.707984924316406\" cy=\"48.813026428222656\" data-type=\"element node\" data-element-id=\"33\" data-node-id=\"33\" data-label-name=\"33\"></circle>\n<circle r=\"0.75\" cx=\"54.590335845947266\" cy=\"49.65336227416992\" data-type=\"element node\" data-element-id=\"34\" data-node-id=\"34\" data-label-name=\"34\"></circle>\n<circle r=\"0.75\" cx=\"59.632354736328125\" cy=\"48.813026428222656\" data-type=\"element node\" data-element-id=\"35\" data-node-id=\"35\" data-label-name=\"35\"></circle>\n<circle r=\"0.75\" cx=\"63.49789810180664\" cy=\"47.132354736328125\" data-type=\"element node\" data-element-id=\"36\" data-node-id=\"36\" data-label-name=\"36\"></circle>\n<circle r=\"0.75\" cx=\"27.195377349853516\" cy=\"22.258403778076172\" data-type=\"element node\" data-element-id=\"37\" data-node-id=\"37\" data-label-name=\"37\"></circle>\n<circle r=\"0.75\" cx=\"31.060924530029297\" cy=\"19.56932830810547\" data-type=\"element node\" data-element-id=\"38\" data-node-id=\"38\" data-label-name=\"38\"></circle>\n<circle r=\"0.75\" cx=\"37.61554718017578\" cy=\"18.39285659790039\" data-type=\"element node\" data-element-id=\"39\" data-node-id=\"39\" data-label-name=\"39\"></circle>\n<circle r=\"0.75\" cx=\"42.32143020629883\" cy=\"22.426469802856445\" data-type=\"element node\" data-element-id=\"40\" data-node-id=\"40\" data-label-name=\"40\"></circle>\n<circle r=\"0.75\" cx=\"37.27941131591797\" cy=\"23.434873580932617\" data-type=\"element node\" data-element-id=\"41\" data-node-id=\"41\" data-label-name=\"41\"></circle>\n<circle r=\"0.75\" cx=\"31.228992462158203\" cy=\"24.275209426879883\" data-type=\"element node\" data-element-id=\"42\" data-node-id=\"42\" data-label-name=\"42\"></circle>\n<circle r=\"0.75\" cx=\"63.83403396606445\" cy=\"21.754201889038086\" data-type=\"element node\" data-element-id=\"43\" data-node-id=\"43\" data-label-name=\"43\"></circle>\n<circle r=\"0.75\" cx=\"67.19538116455078\" cy=\"17.888654708862305\" data-type=\"element node\" data-element-id=\"44\" data-node-id=\"44\" data-label-name=\"44\"></circle>\n<circle r=\"0.75\" cx=\"74.42227172851562\" cy=\"17.72058868408203\" data-type=\"element node\" data-element-id=\"45\" data-node-id=\"45\" data-label-name=\"45\"></circle>\n<circle r=\"0.75\" cx=\"78.2878189086914\" cy=\"19.90546226501465\" data-type=\"element node\" data-element-id=\"46\" data-node-id=\"46\" data-label-name=\"46\"></circle>\n<circle r=\"0.75\" cx=\"74.25420379638672\" cy=\"22.258403778076172\" data-type=\"element node\" data-element-id=\"47\" data-node-id=\"47\" data-label-name=\"47\"></circle>\n<circle r=\"0.75\" cx=\"67.86764526367188\" cy=\"22.93067169189453\" data-type=\"element node\" data-element-id=\"48\" data-node-id=\"48\" data-label-name=\"48\"></circle>\n<circle r=\"0.75\" cx=\"39.632354736328125\" cy=\"62.762603759765625\" data-type=\"element node\" data-element-id=\"49\" data-node-id=\"49\" data-label-name=\"49\"></circle>\n<circle r=\"0.75\" cx=\"44.00210189819336\" cy=\"60.07352828979492\" data-type=\"element node\" data-element-id=\"50\" data-node-id=\"50\" data-label-name=\"50\"></circle>\n<circle r=\"0.75\" cx=\"50.052520751953125\" cy=\"58.056724548339844\" data-type=\"element node\" data-element-id=\"51\" data-node-id=\"51\" data-label-name=\"51\"></circle>\n<circle r=\"0.75\" cx=\"53.918067932128906\" cy=\"59.56932830810547\" data-type=\"element node\" data-element-id=\"52\" data-node-id=\"52\" data-label-name=\"52\"></circle>\n<circle r=\"0.75\" cx=\"58.28781509399414\" cy=\"58.39285659790039\" data-type=\"element node\" data-element-id=\"53\" data-node-id=\"53\" data-label-name=\"53\"></circle>\n<circle r=\"0.75\" cx=\"62.48949432373047\" cy=\"60.24159622192383\" data-type=\"element node\" data-element-id=\"54\" data-node-id=\"54\" data-label-name=\"54\"></circle>\n<circle r=\"0.75\" cx=\"68.5399169921875\" cy=\"62.42647171020508\" data-type=\"element node\" data-element-id=\"55\" data-node-id=\"55\" data-label-name=\"55\"></circle>\n<circle r=\"0.75\" cx=\"63.66596603393555\" cy=\"66.6281509399414\" data-type=\"element node\" data-element-id=\"56\" data-node-id=\"56\" data-label-name=\"56\"></circle>\n<circle r=\"0.75\" cx=\"58.9600830078125\" cy=\"69.14916229248047\" data-type=\"element node\" data-element-id=\"57\" data-node-id=\"57\" data-label-name=\"57\"></circle>\n<circle r=\"0.75\" cx=\"54.08613586425781\" cy=\"70.49369812011719\" data-type=\"element node\" data-element-id=\"58\" data-node-id=\"58\" data-label-name=\"58\"></circle>\n<circle r=\"0.75\" cx=\"49.21218490600586\" cy=\"68.81302642822266\" data-type=\"element node\" data-element-id=\"59\" data-node-id=\"59\" data-label-name=\"59\"></circle>\n<circle r=\"0.75\" cx=\"44.50630187988281\" cy=\"67.46849060058594\" data-type=\"element node\" data-element-id=\"60\" data-node-id=\"60\" data-label-name=\"60\"></circle>\n<circle r=\"0.75\" cx=\"44.170169830322266\" cy=\"62.762603759765625\" data-type=\"element node\" data-element-id=\"61\" data-node-id=\"61\" data-label-name=\"61\"></circle>\n<circle r=\"0.75\" cx=\"48.87604904174805\" cy=\"62.090335845947266\" data-type=\"element node\" data-element-id=\"62\" data-node-id=\"62\" data-label-name=\"62\"></circle>\n<circle r=\"0.75\" cx=\"54.25419998168945\" cy=\"61.92226791381836\" data-type=\"element node\" data-element-id=\"63\" data-node-id=\"63\" data-label-name=\"63\"></circle>\n<circle r=\"0.75\" cx=\"58.9600830078125\" cy=\"61.75419998168945\" data-type=\"element node\" data-element-id=\"64\" data-node-id=\"64\" data-label-name=\"64\"></circle>\n<circle r=\"0.75\" cx=\"64.50630187988281\" cy=\"62.42647171020508\" data-type=\"element node\" data-element-id=\"65\" data-node-id=\"65\" data-label-name=\"65\"></circle>\n<circle r=\"0.75\" cx=\"58.45588302612305\" cy=\"63.60293960571289\" data-type=\"element node\" data-element-id=\"66\" data-node-id=\"66\" data-label-name=\"66\"></circle>\n<circle r=\"0.75\" cx=\"54.42226791381836\" cy=\"63.43487548828125\" data-type=\"element node\" data-element-id=\"67\" data-node-id=\"67\" data-label-name=\"67\"></circle>\n<circle r=\"0.75\" cx=\"50.052520751953125\" cy=\"63.60293960571289\" data-type=\"element node\" data-element-id=\"68\" data-node-id=\"68\" data-label-name=\"68\"></circle>"}, {"name": "hands", "type": "skeleton", "attributes": [], "sublabels": [{"name": "1", "type": "points", "attributes": []}, {"name": "2", "type": "points", "attributes": []}, {"name": "3", "type": "points", "attributes": []}, {"name": "4", "type": "points", "attributes": []}, {"name": "5", "type": "points", "attributes": []}, {"name": "6", "type": "points", "attributes": []}, {"name": "7", "type": "points", "attributes": []}, {"name": "8", "type": "points", "attributes": []}, {"name": "9", "type": "points", "attributes": []}, {"name": "10", "type": "points", "attributes": []}, {"name": "11", "type": "points", "attributes": []}, {"name": "12", "type": "points", "attributes": []}, {"name": "13", "type": "points", "attributes": []}, {"name": "14", "type": "points", "attributes": []}, {"name": "15", "type": "points", "attributes": []}, {"name": "16", "type": "points", "attributes": []}, {"name": "17", "type": "points", "attributes": []}, {"name": "18", "type": "points", "attributes": []}, {"name": "19", "type": "points", "attributes": []}, {"name": "20", "type": "points", "attributes": []}, {"name": "21", "type": "points", "attributes": []}, {"name": "22", "type": "points", "attributes": []}, {"name": "23", "type": "points", "attributes": []}, {"name": "24", "type": "points", "attributes": []}, {"name": "25", "type": "points", "attributes": []}, {"name": "26", "type": "points", "attributes": []}, {"name": "27", "type": "points", "attributes": []}, {"name": "28", "type": "points", "attributes": []}, {"name": "29", "type": "points", "attributes": []}, {"name": "30", "type": "points", "attributes": []}, {"name": "31", "type": "points", "attributes": []}, {"name": "32", "type": "points", "attributes": []}, {"name": "33", "type": "points", "attributes": []}, {"name": "34", "type": "points", "attributes": []}, {"name": "35", "type": "points", "attributes": []}, {"name": "36", "type": "points", "attributes": []}, {"name": "37", "type": "points", "attributes": []}, {"name": "38", "type": "points", "attributes": []}, {"name": "39", "type": "points", "attributes": []}, {"name": "40", "type": "points", "attributes": []}, {"name": "41", "type": "points", "attributes": []}, {"name": "42", "type": "points", "attributes": []}], "svg": "<line x1=\"28.035715103149414\" y1=\"33.3508415222168\" x2=\"29.044116973876953\" y2=\"27.3004207611084\" data-type=\"edge\" data-node-from=\"29\" data-node-to=\"30\"></line>\n<line x1=\"26.859243392944336\" y1=\"39.40126037597656\" x2=\"28.035715103149414\" y2=\"33.3508415222168\" data-type=\"edge\" data-node-from=\"28\" data-node-to=\"29\"></line>\n<line x1=\"25.514705657958984\" y1=\"45.619747161865234\" x2=\"26.859243392944336\" y2=\"39.40126037597656\" data-type=\"edge\" data-node-from=\"27\" data-node-to=\"28\"></line>\n<line x1=\"25.514705657958984\" y1=\"45.619747161865234\" x2=\"20.472688674926758\" y2=\"72.84664154052734\" data-type=\"edge\" data-node-from=\"27\" data-node-to=\"22\"></line>\n<line x1=\"14.254201889038086\" y1=\"27.636554718017578\" x2=\"13.75\" y2=\"33.85504150390625\" data-type=\"edge\" data-node-from=\"38\" data-node-to=\"37\"></line>\n<line x1=\"20.472688674926758\" y1=\"30.997900009155273\" x2=\"20.808822631835938\" y2=\"24.77941131591797\" data-type=\"edge\" data-node-from=\"33\" data-node-to=\"34\"></line>\n<line x1=\"20.472688674926758\" y1=\"37.88865661621094\" x2=\"20.472688674926758\" y2=\"30.997900009155273\" data-type=\"edge\" data-node-from=\"32\" data-node-to=\"33\"></line>\n<line x1=\"19.968486785888672\" y1=\"44.77941131591797\" x2=\"20.472688674926758\" y2=\"37.88865661621094\" data-type=\"edge\" data-node-from=\"31\" data-node-to=\"32\"></line>\n<line x1=\"20.472688674926758\" y1=\"72.84664154052734\" x2=\"19.968486785888672\" y2=\"44.77941131591797\" data-type=\"edge\" data-node-from=\"22\" data-node-to=\"31\"></line>\n<line x1=\"14.590335845947266\" y1=\"46.628150939941406\" x2=\"20.472688674926758\" y2=\"72.84664154052734\" data-type=\"edge\" data-node-from=\"35\" data-node-to=\"22\"></line>\n<line x1=\"14.086134910583496\" y1=\"40.409664154052734\" x2=\"14.590335845947266\" y2=\"46.628150939941406\" data-type=\"edge\" data-node-from=\"36\" data-node-to=\"35\"></line>\n<line x1=\"13.75\" y1=\"33.85504150390625\" x2=\"14.086134910583496\" y2=\"40.409664154052734\" data-type=\"edge\" data-node-from=\"37\" data-node-to=\"36\"></line>\n<line x1=\"6.01890754699707\" y1=\"41.75419998168945\" x2=\"3.834033727645874\" y2=\"36.0399169921875\" data-type=\"edge\" data-node-from=\"41\" data-node-to=\"42\"></line>\n<line x1=\"7.363445281982422\" y1=\"45.95588302612305\" x2=\"6.01890754699707\" y2=\"41.75419998168945\" data-type=\"edge\" data-node-from=\"40\" data-node-to=\"41\"></line>\n<line x1=\"9.04411792755127\" y1=\"50.829830169677734\" x2=\"7.363445281982422\" y2=\"45.95588302612305\" data-type=\"edge\" data-node-from=\"39\" data-node-to=\"40\"></line>\n<line x1=\"20.472688674926758\" y1=\"72.84664154052734\" x2=\"9.04411792755127\" y2=\"50.829830169677734\" data-type=\"edge\" data-node-from=\"22\" data-node-to=\"39\"></line>\n<line x1=\"38.792015075683594\" y1=\"54.19117736816406\" x2=\"46.0189094543457\" y2=\"52.342437744140625\" data-type=\"edge\" data-node-from=\"25\" data-node-to=\"26\"></line>\n<line x1=\"32.405460357666016\" y1=\"60.24159622192383\" x2=\"38.792015075683594\" y2=\"54.19117736816406\" data-type=\"edge\" data-node-from=\"24\" data-node-to=\"25\"></line>\n<line x1=\"25.514705657958984\" y1=\"67.80461883544922\" x2=\"32.405460357666016\" y2=\"60.24159622192383\" data-type=\"edge\" data-node-from=\"23\" data-node-to=\"24\"></line>\n<line x1=\"20.472688674926758\" y1=\"72.84664154052734\" x2=\"25.514705657958984\" y2=\"67.80461883544922\" data-type=\"edge\" data-node-from=\"22\" data-node-to=\"23\"></line>\n<line x1=\"80.13655090332031\" y1=\"44.947479248046875\" x2=\"79.80042266845703\" y2=\"72.51050567626953\" data-type=\"edge\" data-node-from=\"10\" data-node-to=\"1\"></line>\n<line x1=\"79.80042266845703\" y1=\"72.51050567626953\" x2=\"85.17857360839844\" y2=\"46.79621887207031\" data-type=\"edge\" data-node-from=\"1\" data-node-to=\"14\"></line>\n<line x1=\"90.55672454833984\" y1=\"50.829830169677734\" x2=\"79.80042266845703\" y2=\"72.51050567626953\" data-type=\"edge\" data-node-from=\"18\" data-node-to=\"1\"></line>\n<line x1=\"92.23739624023438\" y1=\"46.4600830078125\" x2=\"90.55672454833984\" y2=\"50.829830169677734\" data-type=\"edge\" data-node-from=\"19\" data-node-to=\"18\"></line>\n<line x1=\"93.9180679321289\" y1=\"41.92226791381836\" x2=\"92.23739624023438\" y2=\"46.4600830078125\" data-type=\"edge\" data-node-from=\"20\" data-node-to=\"19\"></line>\n<line x1=\"95.59873962402344\" y1=\"35.871849060058594\" x2=\"93.9180679321289\" y2=\"41.92226791381836\" data-type=\"edge\" data-node-from=\"21\" data-node-to=\"20\"></line>\n<line x1=\"85.68276977539062\" y1=\"34.023109436035156\" x2=\"85.68276977539062\" y2=\"26.964284896850586\" data-type=\"edge\" data-node-from=\"16\" data-node-to=\"17\"></line>\n<line x1=\"85.68276977539062\" y1=\"40.07352828979492\" x2=\"85.68276977539062\" y2=\"34.023109436035156\" data-type=\"edge\" data-node-from=\"15\" data-node-to=\"16\"></line>\n<line x1=\"85.17857360839844\" y1=\"46.79621887207031\" x2=\"85.68276977539062\" y2=\"40.07352828979492\" data-type=\"edge\" data-node-from=\"14\" data-node-to=\"15\"></line>\n<line x1=\"79.80042266845703\" y1=\"37.552520751953125\" x2=\"80.13655090332031\" y2=\"44.947479248046875\" data-type=\"edge\" data-node-from=\"11\" data-node-to=\"10\"></line>\n<line x1=\"79.63235473632812\" y1=\"30.997900009155273\" x2=\"79.80042266845703\" y2=\"37.552520751953125\" data-type=\"edge\" data-node-from=\"12\" data-node-to=\"11\"></line>\n<line x1=\"79.63235473632812\" y1=\"24.611345291137695\" x2=\"79.63235473632812\" y2=\"30.997900009155273\" data-type=\"edge\" data-node-from=\"13\" data-node-to=\"12\"></line>\n<line x1=\"70.89286041259766\" y1=\"26.796218872070312\" x2=\"72.06932830810547\" y2=\"33.18277359008789\" data-type=\"edge\" data-node-from=\"9\" data-node-to=\"8\"></line>\n<line x1=\"72.90966033935547\" y1=\"39.56932830810547\" x2=\"72.06932830810547\" y2=\"33.18277359008789\" data-type=\"edge\" data-node-from=\"7\" data-node-to=\"8\"></line>\n<line x1=\"74.42227172851562\" y1=\"46.12395095825195\" x2=\"72.90966033935547\" y2=\"39.56932830810547\" data-type=\"edge\" data-node-from=\"6\" data-node-to=\"7\"></line>\n<line x1=\"79.80042266845703\" y1=\"72.51050567626953\" x2=\"74.42227172851562\" y2=\"46.12395095825195\" data-type=\"edge\" data-node-from=\"1\" data-node-to=\"6\"></line>\n<line x1=\"61.6491584777832\" y1=\"54.52730941772461\" x2=\"54.42226791381836\" y2=\"52.51050567626953\" data-type=\"edge\" data-node-from=\"4\" data-node-to=\"5\"></line>\n<line x1=\"68.03571319580078\" y1=\"60.91386413574219\" x2=\"61.6491584777832\" y2=\"54.52730941772461\" data-type=\"edge\" data-node-from=\"3\" data-node-to=\"4\"></line>\n<line x1=\"74.59033966064453\" y1=\"67.13235473632812\" x2=\"68.03571319580078\" y2=\"60.91386413574219\" data-type=\"edge\" data-node-from=\"2\" data-node-to=\"3\"></line>\n<line x1=\"79.80042266845703\" y1=\"72.51050567626953\" x2=\"74.59033966064453\" y2=\"67.13235473632812\" data-type=\"edge\" data-node-from=\"1\" data-node-to=\"2\"></line>\n<circle r=\"0.75\" cx=\"79.80042266845703\" cy=\"72.51050567626953\" data-type=\"element node\" data-element-id=\"1\" data-node-id=\"1\" data-label-name=\"1\"></circle>\n<circle r=\"0.75\" cx=\"74.59033966064453\" cy=\"67.13235473632812\" data-type=\"element node\" data-element-id=\"2\" data-node-id=\"2\" data-label-name=\"2\"></circle>\n<circle r=\"0.75\" cx=\"68.03571319580078\" cy=\"60.91386413574219\" data-type=\"element node\" data-element-id=\"3\" data-node-id=\"3\" data-label-name=\"3\"></circle>\n<circle r=\"0.75\" cx=\"61.6491584777832\" cy=\"54.52730941772461\" data-type=\"element node\" data-element-id=\"4\" data-node-id=\"4\" data-label-name=\"4\"></circle>\n<circle r=\"0.75\" cx=\"54.42226791381836\" cy=\"52.51050567626953\" data-type=\"element node\" data-element-id=\"5\" data-node-id=\"5\" data-label-name=\"5\"></circle>\n<circle r=\"0.75\" cx=\"74.42227172851562\" cy=\"46.12395095825195\" data-type=\"element node\" data-element-id=\"6\" data-node-id=\"6\" data-label-name=\"6\"></circle>\n<circle r=\"0.75\" cx=\"72.90966033935547\" cy=\"39.56932830810547\" data-type=\"element node\" data-element-id=\"7\" data-node-id=\"7\" data-label-name=\"7\"></circle>\n<circle r=\"0.75\" cx=\"72.06932830810547\" cy=\"33.18277359008789\" data-type=\"element node\" data-element-id=\"8\" data-node-id=\"8\" data-label-name=\"8\"></circle>\n<circle r=\"0.75\" cx=\"70.89286041259766\" cy=\"26.796218872070312\" data-type=\"element node\" data-element-id=\"9\" data-node-id=\"9\" data-label-name=\"9\"></circle>\n<circle r=\"0.75\" cx=\"80.13655090332031\" cy=\"44.947479248046875\" data-type=\"element node\" data-element-id=\"10\" data-node-id=\"10\" data-label-name=\"10\"></circle>\n<circle r=\"0.75\" cx=\"79.80042266845703\" cy=\"37.552520751953125\" data-type=\"element node\" data-element-id=\"11\" data-node-id=\"11\" data-label-name=\"11\"></circle>\n<circle r=\"0.75\" cx=\"79.63235473632812\" cy=\"30.997900009155273\" data-type=\"element node\" data-element-id=\"12\" data-node-id=\"12\" data-label-name=\"12\"></circle>\n<circle r=\"0.75\" cx=\"79.63235473632812\" cy=\"24.611345291137695\" data-type=\"element node\" data-element-id=\"13\" data-node-id=\"13\" data-label-name=\"13\"></circle>\n<circle r=\"0.75\" cx=\"85.17857360839844\" cy=\"46.79621887207031\" data-type=\"element node\" data-element-id=\"14\" data-node-id=\"14\" data-label-name=\"14\"></circle>\n<circle r=\"0.75\" cx=\"85.68276977539062\" cy=\"40.07352828979492\" data-type=\"element node\" data-element-id=\"15\" data-node-id=\"15\" data-label-name=\"15\"></circle>\n<circle r=\"0.75\" cx=\"85.68276977539062\" cy=\"34.023109436035156\" data-type=\"element node\" data-element-id=\"16\" data-node-id=\"16\" data-label-name=\"16\"></circle>\n<circle r=\"0.75\" cx=\"85.68276977539062\" cy=\"26.964284896850586\" data-type=\"element node\" data-element-id=\"17\" data-node-id=\"17\" data-label-name=\"17\"></circle>\n<circle r=\"0.75\" cx=\"90.55672454833984\" cy=\"50.829830169677734\" data-type=\"element node\" data-element-id=\"18\" data-node-id=\"18\" data-label-name=\"18\"></circle>\n<circle r=\"0.75\" cx=\"92.23739624023438\" cy=\"46.4600830078125\" data-type=\"element node\" data-element-id=\"19\" data-node-id=\"19\" data-label-name=\"19\"></circle>\n<circle r=\"0.75\" cx=\"93.9180679321289\" cy=\"41.92226791381836\" data-type=\"element node\" data-element-id=\"20\" data-node-id=\"20\" data-label-name=\"20\"></circle>\n<circle r=\"0.75\" cx=\"95.59873962402344\" cy=\"35.871849060058594\" data-type=\"element node\" data-element-id=\"21\" data-node-id=\"21\" data-label-name=\"21\"></circle>\n<circle r=\"0.75\" cx=\"20.472688674926758\" cy=\"72.84664154052734\" data-type=\"element node\" data-element-id=\"22\" data-node-id=\"22\" data-label-name=\"22\"></circle>\n<circle r=\"0.75\" cx=\"25.514705657958984\" cy=\"67.80461883544922\" data-type=\"element node\" data-element-id=\"23\" data-node-id=\"23\" data-label-name=\"23\"></circle>\n<circle r=\"0.75\" cx=\"32.405460357666016\" cy=\"60.24159622192383\" data-type=\"element node\" data-element-id=\"24\" data-node-id=\"24\" data-label-name=\"24\"></circle>\n<circle r=\"0.75\" cx=\"38.792015075683594\" cy=\"54.19117736816406\" data-type=\"element node\" data-element-id=\"25\" data-node-id=\"25\" data-label-name=\"25\"></circle>\n<circle r=\"0.75\" cx=\"46.0189094543457\" cy=\"52.342437744140625\" data-type=\"element node\" data-element-id=\"26\" data-node-id=\"26\" data-label-name=\"26\"></circle>\n<circle r=\"0.75\" cx=\"25.514705657958984\" cy=\"45.619747161865234\" data-type=\"element node\" data-element-id=\"27\" data-node-id=\"27\" data-label-name=\"27\"></circle>\n<circle r=\"0.75\" cx=\"26.859243392944336\" cy=\"39.40126037597656\" data-type=\"element node\" data-element-id=\"28\" data-node-id=\"28\" data-label-name=\"28\"></circle>\n<circle r=\"0.75\" cx=\"28.035715103149414\" cy=\"33.3508415222168\" data-type=\"element node\" data-element-id=\"29\" data-node-id=\"29\" data-label-name=\"29\"></circle>\n<circle r=\"0.75\" cx=\"29.044116973876953\" cy=\"27.3004207611084\" data-type=\"element node\" data-element-id=\"30\" data-node-id=\"30\" data-label-name=\"30\"></circle>\n<circle r=\"0.75\" cx=\"19.968486785888672\" cy=\"44.77941131591797\" data-type=\"element node\" data-element-id=\"31\" data-node-id=\"31\" data-label-name=\"31\"></circle>\n<circle r=\"0.75\" cx=\"20.472688674926758\" cy=\"37.88865661621094\" data-type=\"element node\" data-element-id=\"32\" data-node-id=\"32\" data-label-name=\"32\"></circle>\n<circle r=\"0.75\" cx=\"20.472688674926758\" cy=\"30.997900009155273\" data-type=\"element node\" data-element-id=\"33\" data-node-id=\"33\" data-label-name=\"33\"></circle>\n<circle r=\"0.75\" cx=\"20.808822631835938\" cy=\"24.77941131591797\" data-type=\"element node\" data-element-id=\"34\" data-node-id=\"34\" data-label-name=\"34\"></circle>\n<circle r=\"0.75\" cx=\"14.590335845947266\" cy=\"46.628150939941406\" data-type=\"element node\" data-element-id=\"35\" data-node-id=\"35\" data-label-name=\"35\"></circle>\n<circle r=\"0.75\" cx=\"14.086134910583496\" cy=\"40.409664154052734\" data-type=\"element node\" data-element-id=\"36\" data-node-id=\"36\" data-label-name=\"36\"></circle>\n<circle r=\"0.75\" cx=\"13.75\" cy=\"33.85504150390625\" data-type=\"element node\" data-element-id=\"37\" data-node-id=\"37\" data-label-name=\"37\"></circle>\n<circle r=\"0.75\" cx=\"14.254201889038086\" cy=\"27.636554718017578\" data-type=\"element node\" data-element-id=\"38\" data-node-id=\"38\" data-label-name=\"38\"></circle>\n<circle r=\"0.75\" cx=\"9.04411792755127\" cy=\"50.829830169677734\" data-type=\"element node\" data-element-id=\"39\" data-node-id=\"39\" data-label-name=\"39\"></circle>\n<circle r=\"0.75\" cx=\"7.363445281982422\" cy=\"45.95588302612305\" data-type=\"element node\" data-element-id=\"40\" data-node-id=\"40\" data-label-name=\"40\"></circle>\n<circle r=\"0.75\" cx=\"6.01890754699707\" cy=\"41.75419998168945\" data-type=\"element node\" data-element-id=\"41\" data-node-id=\"41\" data-label-name=\"41\"></circle>\n<circle r=\"0.75\" cx=\"3.834033727645874\" cy=\"36.0399169921875\" data-type=\"element node\" data-element-id=\"42\" data-node-id=\"42\" data-label-name=\"42\"></circle>"}], "description": "Whole Body points", "name": "Human pose estimation", "version": 1}]
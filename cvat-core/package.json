{"name": "cvat-core", "version": "15.3.1", "type": "module", "description": "Part of Computer Vision Tool which presents an interface for client-side integration", "main": "src/api.ts", "scripts": {"build": "webpack", "type-check": "tsc --noEmit", "type-check:watch": "yarn run type-check --watch"}, "author": "CVAT.ai", "license": "MIT", "browserslist": ["Chrome >= 63", "Firefox > 102", "not IE 11", "> 2%"], "devDependencies": {"@babel/preset-typescript": "^7.23.3", "babel-plugin-transform-import-meta": "^2.2.1"}, "dependencies": {"axios": "^1.7.4", "axios-retry": "^4.0.0", "cvat-data": "link:./../cvat-data", "detect-browser": "^5.2.1", "error-stack-parser": "^2.0.2", "form-data": "^4.0.0", "js-cookie": "^3.0.1", "json-logic-js": "^2.0.1", "platform": "^1.3.5", "quickhull": "^1.0.3", "store": "^2.0.12", "tus-js-client": "^3.0.1"}}
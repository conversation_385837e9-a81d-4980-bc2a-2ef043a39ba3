// Copyright (C) CVAT.ai Corporation
//
// SPDX-License-Identifier: MIT

@import 'base';

.cvat-brush-tools-toolbox {
    position: absolute;
    margin: 20px;
    padding: 10px;
    border-radius: $border-radius-base;
    background: #333;
    display: flex;
    align-items: center;
    z-index: 100;
    box-shadow: $box-shadow-base;

    > hr {
        width: 1px;
        height: $grid-unit-size * 4;
        background: $border-color-1;
    }

    > * {
        margin-right: $grid-unit-size;
    }

    > button {
        font-size: 20px;

        > span.anticon {
            font-size: inherit;
        }
    }

    > button:disabled {
        opacity: .5;
    }

    .cvat-brush-tools-brush,
    .cvat-brush-tools-eraser {
        svg {
            width: 24px;
            height: 25px;
        }
    }

    .cvat-brush-tools-draggable-area {
        display: flex;
        font-size: 20px;

        svg {
            width: 24px;
            height: 25px;
        }
    }

    .cvat-brush-tools-active-tool {
        background: $header-color;
    }
}

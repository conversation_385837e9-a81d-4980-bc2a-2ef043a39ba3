// Copyright (C) 2020-2022 Intel Corporation
// Copyright (C) CVAT.ai Corporation
//
// SPDX-License-Identifier: MIT

@import 'base';

.cvat-canvas3d-perspective {
    height: 100%;
    width: 100%;
    position: relative;
    padding: $grid-unit-size * 0.5;
}

.cvat-canvas3d-orthographic-views {
    height: 50%;
    position: relative;
    display: flex;
    flex-direction: row;
    width: 100%;
}

.cvat-canvas3d-perspective-arrow-directions {
    position: absolute;
    padding: $grid-unit-size;
    bottom: 0;
    right: 0;

    > div {
        text-align: center;
    }
}

.cvat-canvas3d-perspective-arrow-directions-icons-right,
.cvat-canvas3d-perspective-arrow-directions-icons-left {
    margin: $grid-unit-size;
}

.cvat-canvas3d-perspective-arrow-directions-icons-color {
    color: black;
}

.cvat-canvas3d-perspective-directions {
    padding: $grid-unit-size;
    position: absolute;
    bottom: 0;
    left: 0;
}

.cvat-canvas3d-perspective-directions-icon {
    margin: $grid-unit-size * 0.5 $grid-unit-size * 0.25;
    width: $grid-unit-size * 4;
}

.cvat-canvas3d-orthographic-view {
    width: 100%;
    height: 100%;
    padding-top: $grid-unit-size * 0.5;
    padding-bottom: $grid-unit-size * 0.5;
}

.cvat-canvas3d-topview {
    padding-left: $grid-unit-size * 0.5;
    padding-right: $grid-unit-size * 0.5;
}

.cvat-canvas3d-sideview {
    padding-right: $grid-unit-size * 0.5;
}

.cvat-canvas3d-frontview {
    padding-right: $grid-unit-size * 0.5;
}

.cvat-canvas3d-fullsize {
    width: 100%;
    height: calc(100% - $grid-unit-size * 3);
}

.cvat-canvas3d-header {
    height: $grid-unit-size * 3;
    width: 100%;
    background-color: $background-color-2;
    text-align: center;
    vertical-align: middle;
}

.cvat-canvas-container-overflow {
    overflow: hidden;
    width: 100%;
    height: 100%;
}

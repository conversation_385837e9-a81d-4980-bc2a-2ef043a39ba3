// Copyright (C) 2020-2022 Intel Corporation
//
// SPDX-License-Identifier: MIT

import React from 'react';
import Icon from '@ant-design/icons';

import { RedoIcon } from 'icons';
import CVATTooltip from 'components/common/cvat-tooltip';

export interface Props {
    redo: () => void;
}

function RedoControl(props: Props): JSX.Element {
    const { redo } = props;

    return (
        <CVATTooltip title='Redo' placement='right'>
            <Icon
                component={RedoIcon}
                className='cvat-redo-control'
                onClick={(): void => redo()}
            />
        </CVATTooltip>
    );
}

export default React.memo(RedoControl);

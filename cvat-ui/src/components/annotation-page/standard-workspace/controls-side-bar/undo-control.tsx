// Copyright (C) 2020-2022 Intel Corporation
//
// SPDX-License-Identifier: MIT

import React from 'react';
import Icon from '@ant-design/icons';

import { UndoIcon } from 'icons';
import CVATTooltip from 'components/common/cvat-tooltip';

export interface Props {
    undo: () => void;
}

function UndoControl(props: Props): JSX.Element {
    const { undo } = props;

    return (
        <CVATTooltip title='Undo' placement='right'>
            <Icon
                component={UndoIcon}
                className='cvat-undo-control'
                onClick={(): void => undo()}
            />
        </CVATTooltip>
    );
}

export default React.memo(UndoControl);

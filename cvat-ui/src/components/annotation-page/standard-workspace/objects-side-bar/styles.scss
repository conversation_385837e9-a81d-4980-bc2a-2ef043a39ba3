// Copyright (C) 2020-2022 Intel Corporation
// Copyright (C) CVAT.ai Corporation
//
// SPDX-License-Identifier: MIT

@import 'base';

.cvat-objects-appearance-collapse.ant-collapse {
    width: 100%;
    border-radius: 12px;
    background-color: rgba(0, 0, 0, 30%);

    > .ant-collapse-item {
        border: none;

        > .ant-collapse-header {
            padding-top: 2.5px;
            padding-bottom: 2.5px;

            // background: $header-color;
            border-radius: 0;
            height: 25px;

            .ant-collapse-arrow {
                padding: 0;
                top: $grid-unit-size;
            }
        }

        > .ant-collapse-content {
            background: $background-color-2;
            border-bottom: none;
            padding-bottom: $grid-unit-size * 3;

            > .ant-collapse-content-box {
                padding: 10px;
                padding-bottom: 0;
            }
        }
    }
}

.cvat-object-sidebar-icon {
    fill: $objects-bar-icons-color;
    color: $objects-bar-icons-color;
    font-size: 15px;

    &:hover {
        transform: scale(1.1);
    }
}

.cvat-objects-sidebar-tabs.ant-tabs.ant-tabs-card {
    box-sizing: border-box;
    border: none;

    .ant-tabs-nav {
        &::before {
            content: none;
        }

        border: none;
        margin-bottom: 0;

        .ant-tabs-tab.ant-tabs-tab-active {
            opacity: 1;

            // background: $objects-bar-tabs-color;
        }

        .ant-tabs-tab {
            padding: $grid-unit-size $grid-unit-size * 2 $grid-unit-size $grid-unit-size * 2;
            background: none;
            border: none;
            user-select: none;
            border-radius: $grid-unit-size $grid-unit-size 0 0;
        }
    }
}

.cvat-objects-sidebar-labels-list-header {
    background: $objects-bar-tabs-color;
    padding: $grid-unit-size;
    border-bottom: 1px solid $border-color-1;
}

.cvat-objects-sidebar-issues-list-header {
    background: $objects-bar-tabs-color;
    padding: $grid-unit-size;
    box-sizing: border-box;
    border-bottom: 1px solid $border-color-1;

    > div > div {
        > span[role='img'] {
            font-size: 16px;
            color: $objects-bar-icons-color;

            &:hover {
                transform: scale(1.1);
                opacity: 0.8;
            }

            &:active {
                transform: scale(1);
                opacity: 0.7;
            }
        }
    }
}

.cvat-objects-sidebar-issues-list {
    background-color: $background-color-2;
    height: calc(100% - $grid-unit-size * 4);
    overflow: hidden auto;
}

.cvat-objects-sidebar-issue-item {
    width: 100%;
    margin: 1px;
    padding: $grid-unit-size;
    border-left: 3px solid #ff7300;
    border-bottom: 1px solid $border-color-1;

    &:hover {
        border-left: 4px solid #ff7300;
    }

    > .ant-alert.ant-alert-with-description {
        padding: $grid-unit-size $grid-unit-size $grid-unit-size $grid-unit-size * 8;
    }

    > .ant-row {
        .ant-typography {
            margin-bottom: 0;
        }
    }

    p {
        margin-bottom: 0;
    }
}

.cvat-objects-sidebar-conflict-item {
    width: 100%;
    margin: 1px;
    padding: $grid-unit-size;
    border-left: 3px solid #e10602;
    border-bottom: 1px solid $border-color-1;

    p {
        margin-bottom: 0;
    }
}

.cvat-objects-sidebar-warning-item {
    @extend .cvat-objects-sidebar-issue-item;
}

.cvat-objects-sidebar-item-active {
    border-left-width: 4px;
}

.cvat-objects-sidebar-issue-resolved {
    border-left: 3px solid $border-color-1;

    &:hover {
        border-left: 4px solid $border-color-1;
    }
}

.cvat-objects-sidebar-states-header {
    // background: $objects-bar-tabs-color;
    padding: $grid-unit-size $grid-unit-size * 2 $grid-unit-size $grid-unit-size * 2;
    line-height: $grid-unit-size * 3;

    // border-bottom: 1px solid $border-color-1;

    > div {
        > div:nth-child(1) {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;

            .cvat-objects-sidebar-ordering-selector {
                margin-left: $grid-unit-size;
                width: $grid-unit-size * 16;
            }
        }

        > div:nth-child(2) {
            width: 100%;
            margin-top: $grid-unit-size;

            span[role='img'] {
                &:not(:first-child) {
                    margin-left: $grid-unit-size;
                }

                @extend .cvat-object-sidebar-icon;
            }
        }
    }

    .cvat-objects-sidebar-show-ground-truth {
        margin-right: $grid-unit-size;
    }
}

.cvat-objects-sidebar-states-list {
    background-color: $background-color-2;
    height: calc(100% - $grid-unit-size * 9);
    overflow-y: auto;
}

.cvat-objects-sidebar-z-layer-mark {
    text-align: center;
}

.cvat-objects-sidebar-state-item.cvat-objects-sidebar-state-active-item {
    background-color: #ffffff1a;
    border-radius: 20px;
}

.cvat-objects-sidebar-state-item {
    width: 100%;
    transition: box-shadow $box-shadow-transition;
    padding: $grid-unit-size * 0.5 20px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    &:not(:first-child) {
        margin-top: $grid-unit-size * 0.25;
    }

    &:not(:last-child) {
        margin-bottom: $grid-unit-size * 0.25;
    }

    > div:nth-child(1) {
        > div:nth-child(1) {
            line-height: 12px;
        }

        > div:nth-child(3) > span {
            font-size: 25px;
        }

        > div:nth-child(2) > .ant-select {
            width: 100%;
        }
    }

    > div:nth-child(2) {
        > div > div {
            margin-top: 5px;
        }

        span[role='img'] {
            @extend .cvat-object-sidebar-icon;
        }
    }
}

.cvat-objects-sidebar-state-item-collapse,
.cvat-objects-sidebar-state-item-elements-collapse {
    border: unset;
    background: unset;
    width: 100%;

    > .ant-collapse-item {
        background: inherit;
        border: none;

        > .ant-collapse-header {
            align-items: baseline;
            padding-top: 2px;
            padding-bottom: 2px;

            .ant-collapse-arrow {
                padding: 0;
                top: $grid-unit-size;
            }
        }

        > .ant-collapse-content {
            > .ant-collapse-content-box {
                padding: 0;
            }

            border: unset;
            background: inherit;
        }
    }
}

.cvat-object-item-attribute-wrapper {
    margin-top: 5px;
}

.cvat-object-item-select-attribute {
    width: 100%;
}

.cvat-object-item-number-attribute {
    width: 100%;
}

.cvat-object-item-text-attribute {
    width: 100%;
}

.cvat-object-item-radio-attribute {
    border: 1px double $border-color-hover;
    border-radius: $border-radius-base;

    > legend {
        text-align: center;
        width: unset;
        text-overflow: ellipsis;
        max-width: 80%;
        overflow: hidden;
        max-height: 1.2em;
        font-size: 1em;
        margin: 0;

        > span {
            padding: 0 10px;
        }
    }

    > .ant-radio-group {
        display: grid;
        padding: 1px 5px;
    }
}

.cvat-objects-sidebar-labels-list {
    background-color: $background-color-2;
    height: 100%;
    overflow-y: auto;
}

.cvat-objects-sidebar-label-active-item {
    background: $active-label-background-color;
    box-shadow: $box-shadow-base;
    border: 1px solid $object-item-border-color;
}

.cvat-objects-sidebar-label-item {
    height: 2.5em;
    transition: box-shadow $box-shadow-transition;
    border: 1px solid $border-color-1;
    padding: $grid-unit-size * 0.5;
    margin-left: $grid-unit-size * 0.25;
    margin-right: $grid-unit-size * 0.25;

    span {
        @extend .cvat-object-sidebar-icon;
    }

    > div:nth-child(2) {
        text-overflow: ellipsis;
        overflow: hidden;
        max-height: 1.5em;
        font-size: 1em;
    }

    &:hover {
        @extend .cvat-objects-sidebar-label-active-item;
    }
}

.cvat-label-item-color {
    background: rgb(25, 184, 14);
    height: 80%;
    width: 90%;
    border-radius: $border-radius-base;
}

.cvat-objects-appearance-content {
    > div {
        width: 100%;

        > label {
            text-align: center;
            width: 33%;
        }
    }

    .cvat-appearance-color-by-radio-group {
        width: 100%;

        .ant-radio-button-wrapper-checked {
            color: #000;
        }

        .ant-radio-button-wrapper {
            width: 50%;
        }
    }

    .ant-checkbox-wrapper {
        margin-left: 0;
    }

    .ant-checkbox {
        .ant-checkbox-inner {
            border-color: #8aaa21;
        }
    }
}

.cvat-context-menu-item.ant-menu-item {
    &:hover {
        background: $hover-menu-color;
    }
}

.ant-dropdown-menu.cvat-object-item-menu {
    > li.ant-dropdown-menu-item {
        padding: 0;
    }

    button {
        &:not(:disabled) {
            color: $text-color;
        }

        width: 100%;
        height: 100%;
        text-align: left;
    }
}

.cvat-label-color-picker .sketch-picker {
    box-shadow: unset !important;
}

.cvat-states-ordering-selector {
    :first-child {
        margin-right: $grid-unit-size;
    }
}

.cvat-objects-sidebar-label-item-disabled {
    opacity: 0.5;
}

.cvat-workspace-settings-approx-poly-threshold {
    .ant-slider-track {
        background: linear-gradient(90deg, #1890ff 0%, #61c200 100%);
    }
}

.cvat-approx-poly-threshold-wrapper {
    @extend .cvat-workspace-settings-approx-poly-threshold;

    width: $grid-unit-size * 32;
    position: absolute;
    background: $background-color-2;
    top: 8px;
    left: 50%;
    border-radius: $border-radius-base;
    border: 1px solid $border-color-1;
    z-index: 100;
    padding: $grid-unit-size * 0.5 $grid-unit-size * 2 $grid-unit-size * 0.5 $grid-unit-size * 0.5;

    .ant-slider-mark {
        position: static;
        margin-top: 4px;
        pointer-events: none;
    }
}

.cvat-objects-sidebar-state-item-elements {
    padding: $grid-unit-size * 0.5;
    border: 1px solid rgba($color: white, $alpha: 0%);
}

.cvat-objects-sidebar-state-item-elements.cvat-objects-sidebar-state-active-element {
    border: 1px dashed $object-item-border-color;
}

.cvat-objects-sidebar-show-ground-truth-active {
    path {
        fill: #1890ff;
    }
}

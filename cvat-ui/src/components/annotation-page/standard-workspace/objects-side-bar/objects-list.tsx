// Copyright (C) 2020-2022 Intel Corporation
// Copyright (C) CVAT.ai Corporation
//
// SPDX-License-Identifier: MIT

import React, { useMemo } from 'react';

import { StatesOrdering, Workspace } from 'reducers';
import ObjectItemContainer from 'containers/annotation-page/standard-workspace/objects-side-bar/object-item';
import { Label } from 'cvat-core-wrapper';

interface Props {
    workspace: Workspace;
    readonly: boolean;
    statesHidden: boolean;
    statesLocked: boolean;
    statesCollapsedAll: boolean;
    statesOrdering: StatesOrdering;
    sortedStatesID: number[];
    objectStates: any[];
    switchLockAllShortcut: string;
    switchHiddenAllShortcut: string;
    showGroundTruth: boolean;
    labels: Label[];
    activeLabelID: number | null;
    changeStatesOrdering(value: StatesOrdering): void;
    lockAllStates(): void;
    unlockAllStates(): void;
    collapseAllStates(): void;
    expandAllStates(): void;
    hideAllStates(): void;
    showAllStates(): void;
    selectLabel(labelID: number): void;
    changeShowGroundTruth(): void;
}

function ObjectListComponent(props: Props): JSX.Element {
    const {
        readonly, sortedStatesID, objectStates, labels, selectLabel, activeLabelID,
    } = props;
    const serializedLabels = useMemo(
        () => labels.map((label) => ({
            label,
            states: objectStates.filter((item) => item.label.id === label.id),
        })),
        [labels, sortedStatesID],
    );
    return (
        <div className='cvat-objects-sidebar-states-list flex flex-col'>
            {serializedLabels?.map((item) => (
                <div key={item.label.id}>
                    <div
                        className='label-item flex gap-1 w-full items-center py-1 cursor-pointer'
                        role='presentation'
                        onClick={() => item.label.id && selectLabel(item.label.id)}
                    >
                        <span className='w-5 h-5' style={{ backgroundColor: item.label.color }} />
                        <span
                            className={`flex-1 py-1 px-3 rounded-full ${
                                activeLabelID === item.label.id ? 'bg-[#ffffff1a]' : ''
                            }`}
                        >
                            {item.label.name}
                        </span>
                    </div>
                    {item.states.length > 0 ? (
                        <div className='pl-7'>
                            {item.states.map((state) => (
                                <React.Fragment key={state.clientID}>
                                    <ObjectItemContainer
                                        readonly={readonly}
                                        objectStates={objectStates}
                                        clientID={state.clientID}
                                    />
                                </React.Fragment>
                            ))}
                        </div>
                    ) : (
                        <div />
                    )}
                </div>
            ))}
        </div>
    );
}

export default React.memo(ObjectListComponent);

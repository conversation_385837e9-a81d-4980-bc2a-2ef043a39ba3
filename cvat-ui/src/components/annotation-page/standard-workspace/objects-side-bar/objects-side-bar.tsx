// Copyright (C) 2020-2022 Intel Corporation
// Copyright (C) CVAT.ai Corporation
//
// SPDX-License-Identifier: MIT

import './styles.scss';
import React, {
    Dispatch, TransitionEvent, useMemo, useState,
} from 'react';
import { AnyAction } from 'redux';
import { connect, useDispatch } from 'react-redux';
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import Tabs from 'antd/lib/tabs';
import Layout from 'antd/lib/layout';
import {
    Divider, message, Modal, Input, Button,
} from 'antd';

import { CombinedState } from 'reducers';
import { DimensionType } from 'cvat-core-wrapper';
import LabelsList from 'components/annotation-page/standard-workspace/objects-side-bar/labels-list';
import {
    collapseSidebar as collapseSidebarAction,
    removeAnnotationsAsync,
    getJobAsync,
    closeJob,
} from 'actions/annotation-actions';
import AppearanceBlock from 'components/annotation-page/appearance-block';
import IssuesListComponent from 'components/annotation-page/standard-workspace/objects-side-bar/issues-list';
import { CustomButton } from '../../../button/button';
import serverProxy from 'cvat-core/src/server-proxy';
import { frameDataCache } from 'cvat-core/src/frames';

interface OwnProps {
    objectsList: JSX.Element;
}

interface StateToProps {
    sidebarCollapsed: boolean;
    jobInstance: any;
}

interface DispatchToProps {
    collapseSidebar(): void;
}

function mapStateToProps(state: CombinedState): StateToProps {
    const {
        annotation: {
            sidebarCollapsed,
            job: { instance: jobInstance },
        },
    } = state;

    return {
        sidebarCollapsed,
        jobInstance,
    };
}

function mapDispatchToProps(dispatch: Dispatch<AnyAction>): DispatchToProps {
    return {
        collapseSidebar(): void {
            dispatch(collapseSidebarAction());
        },
    };
}

function ObjectsSideBar(props: StateToProps & DispatchToProps & OwnProps): JSX.Element {
    const {
        sidebarCollapsed, collapseSidebar, objectsList, jobInstance,
    } = props;
    const isReviewPage = window.location.href.includes('data-review');
    const isTutorialPage = window.location.href.includes('tutorial');
    const [reviewResult, setReviewResult] = useState<2 | 3>();
    const dispatch = useDispatch();
    const [submitting, setSubmitting] = useState(false);
    const [reminderModalVisible, setReminderModalVisible] = useState(false);
    const [finishSessionReminderModalVisible, setFinishSessionReminderModalVisible] = useState(false);
    const [notCompleteModalVisible, setNotCompleteModalVisible] = useState(false);
    const [tutorialDoneVisible, setTutorialDoneVisible] = useState(false);
    const [captchaModalVisible, setCaptchaModalVisible] = useState(false);
    const [captchaData, setCaptchaData] = useState<{
        code: string;
        image: string;
        key: string;
    } | null>(null);
    const [captchaInput, setCaptchaInput] = useState('');
    const [captchaLoading, setCaptchaLoading] = useState(false);
    const [pendingSubmitData, setPendingSubmitData] = useState<any>(null);

    const collapse = (): void => {
        const [collapser] = window.document.getElementsByClassName('cvat-objects-sidebar');
        const listener = (event: TransitionEvent): void => {
            if (event.target && event.propertyName === 'width' && event.target === collapser) {
                window.dispatchEvent(new Event('resize'));
                (collapser as HTMLElement).removeEventListener('transitionend', listener as any);
            }
        };

        if (collapser) {
            (collapser as HTMLElement).addEventListener('transitionend', listener as any);
        }

        collapseSidebar();
    };

    const is2D = jobInstance ? jobInstance.dimension === DimensionType.DIMENSION_2D : true;

    const yesClassName = useMemo(
        () => `flex-none !flex-col !gap-0 !whitespace-normal !break-word !border !border-[#8aaa21] !bg-black hover:!bg-[#8aaa21] hover:!text-black custom-dark-button text-center cursor-pointer${
            reviewResult === 2 ? ' !bg-[#8aaa21] !text-black' : ''
        }`,
        [reviewResult],
    );

    const noClassName = useMemo(
        () => `flex-none !flex-col !gap-0 !whitespace-normal !break-word !border !border-[#8aaa21] !bg-black hover:!bg-[#8aaa21] hover:!text-black custom-dark-button text-center cursor-pointer${
            reviewResult === 3 ? ' !bg-[#8aaa21] !text-black' : ''
        }`,
        [reviewResult],
    );

    const handleRedo = (): void => {
        // const confirmationResult = window.confirm('Are you sure you want to clear all annotations?');
        // if (confirmationResult) {
        if (isReviewPage) {
            setReviewResult(undefined);
        } else {
            dispatch(removeAnnotationsAsync(0, 0, false));
        }
        // }
    };

    const checkAndShowDailyReminder = () => {
        const today = new Date().toDateString();
        const lastReminder = localStorage.getItem('lastReminderDate');

        const shouldShow = lastReminder !== today;

        if (shouldShow) {
            localStorage.setItem('lastReminderDate', today);
        }

        return shouldShow;
    };

    const confirm = async () => {
        await dispatch(removeAnnotationsAsync(0, 0, false));
        dispatch(closeJob());
        dispatch(
            getJobAsync({
                taskID: jobInstance.taskId || NaN,
                jobID: jobInstance.projectData.projectId,
                job: jobInstance,
                initialFrame: null,
                initialFilters: [],
                fetching: true,
                queryParameters: {
                    initialOpenGuide: false,
                    initialWorkspace: null,
                    defaultLabel: null,
                    defaultPointsCount: null,
                },
            }),
        );
    };

    const proceedWithSubmit = async (submitData: any, key: string, code: string, extra: string) => {
        try {
            if (isReviewPage) {
                await serverProxy.jobs.submitAppAuditTask(
                    submitData.projectId,
                    submitData.id,
                    submitData.auditTaskId,
                    submitData.reviewResult,
                );
            } else {
                await serverProxy.jobs.submitHMTask(
                    submitData.tagTaskId,
                    submitData.annotations,
                    key,
                    code,
                    extra,
                );
            }

            const shouldShow = checkAndShowDailyReminder();
            if (shouldShow) {
                setReminderModalVisible(true);
            } else {
                await confirm();
            }
        } catch (error: any) {
            message.error(error.message);
        } finally {
            setSubmitting(false);
        }
    };

    const getCaptchaAndProceed = async (submitData: any) => {
        try {
            setCaptchaLoading(true);
            const captchaResponse = await serverProxy.jobs.getCaptcha();

            if (captchaResponse.code) {
                // 如果返回的code有值，直接继续提交逻辑
                await proceedWithSubmit(submitData, captchaResponse.key, captchaResponse.code, jobInstance.projectData.taskKey);
            } else {
                // 如果返回的code没有值，显示验证码弹窗
                setCaptchaData(captchaResponse);
                setPendingSubmitData(submitData);
                setCaptchaModalVisible(true);
            }
        } catch (error: any) {
            message.error(error.message);
        } finally {
            setCaptchaLoading(false);
        }
    };

    const handleConfirm = async (): Promise<void> => {
        try {
            setSubmitting(true);
            if (isReviewPage) {
                if (!reviewResult) {
                    setSubmitting(false);
                    setNotCompleteModalVisible(true);
                    return;
                }

                if (isTutorialPage) {
                    setSubmitting(false);
                    setTutorialDoneVisible(true);
                    return;
                }

                const submitData = {
                    projectId: jobInstance.projectData.projectId,
                    id: jobInstance.projectData.id,
                    auditTaskId: jobInstance.projectData.auditTaskId,
                    reviewResult,
                };

                await getCaptchaAndProceed(submitData);
            } else {
                const annotations = await jobInstance.annotations.export();

                const allLabels = new Set(jobInstance.labels.map((label: any) => label.id));

                const usedLabels = new Set(annotations.shapes.map((shape: any) => shape.label_id));
                const usedLabels2 = new Set(
                    annotations.tracks.filter((track: any) => track.shapes.length).map((track: any) => track.label_id),
                );

                const missingLabels = Array.from(allLabels).filter((id) => !usedLabels.has(id) && !usedLabels2.has(id));
                if (missingLabels.length > 0) {
                    setSubmitting(false);
                    setNotCompleteModalVisible(true);
                    return;
                }

                if (isTutorialPage) {
                    setSubmitting(false);
                    setTutorialDoneVisible(true);
                    return;
                }

                const submitData = {
                    tagTaskId: jobInstance.projectData.tagTaskId,
                    annotations: JSON.stringify(annotations),
                };

                await getCaptchaAndProceed(submitData);
            }
        } catch (error: any) {
            message.error(error.message);
            setSubmitting(false);
        }
    };

    const handleFirstConfirm = async () => {
        await confirm();
        setReminderModalVisible(false);
    };

    const handleFirstFinishSession = async () => {
        window.open('/account/task', '_self');
    };

    const handleFinishSession = () => {
        setFinishSessionReminderModalVisible(true);
        // const shouldShow = checkAndShowDailyReminder();
        // if (shouldShow) {
        //     setFinishSessionReminderModalVisible(true);
        // } else {
        //     window.open('/account/task', '_self');
        // }
    };

    const handleCaptchaVerify = async () => {
        if (!captchaInput.trim()) {
            message.error('Please enter the captcha code');
            return;
        }

        if (!captchaData || !pendingSubmitData) {
            message.error('Invalid captcha data');
            return;
        }

        try {
            setCaptchaLoading(true);
            await proceedWithSubmit(pendingSubmitData, captchaData.key, captchaInput, jobInstance.projectData.taskKey);
            setCaptchaModalVisible(false);
            setCaptchaInput('');
            setCaptchaData(null);
            setPendingSubmitData(null);
        } catch (error: any) {
            message.error(error.message);
        } finally {
            setCaptchaLoading(false);
        }
    };

    const handleCaptchaRefresh = async () => {
        try {
            setCaptchaLoading(true);
            const captchaResponse = await serverProxy.jobs.getCaptcha();
            setCaptchaData(captchaResponse);
        } catch (error: any) {
            message.error(error.message);
        } finally {
            setCaptchaLoading(false);
        }
    };

    const handleCaptchaCancel = () => {
        setCaptchaModalVisible(false);
        setCaptchaInput('');
        setCaptchaData(null);
        setPendingSubmitData(null);
        setSubmitting(false);
    };

    const urlParams = new URLSearchParams(window.location.search);
    const redirectParam = urlParams.get('redirect');

    return (
        <Layout.Sider
            className='cvat-objects-sidebar'
            theme='dark'
            width={300}
            collapsedWidth={0}
            reverseArrow
            collapsible
            trigger={null}
            collapsed={sidebarCollapsed}
        >
            <div className='cvat-objects-sidebar-tabs'>
                <div className='text-xl'>Labels</div>
                <Divider />
                {objectsList}
            </div>
            {!sidebarCollapsed && !isReviewPage && <AppearanceBlock />}
            {isReviewPage && (
                <div className='flex flex-col gap-3 mb-5'>
                    <Divider />
                    <div className='text-xl'>Are All Targets accurately labeled?</div>
                    <div
                        className={yesClassName}
                        onClick={() => {
                            setReviewResult(2);
                        }}
                    >
                        YES
                    </div>
                    <div
                        className={noClassName}
                        onClick={() => {
                            setReviewResult(3);
                        }}
                    >
                        NO
                    </div>
                </div>
            )}
            <div className='mt-4 flex gap-3'>
                <div className='flex-1 flex flex-col gap-3'>
                    <CustomButton
                        className='confirm'
                        onClick={handleConfirm}
                        loading={submitting}
                        disabled={submitting}
                    >
                        Confirm
                    </CustomButton>
                    <CustomButton mode='light' onClick={handleRedo} disabled={submitting}>
                        Redo
                    </CustomButton>
                </div>
                <CustomButton
                    className='finish-session !w-32 !h-full flex-none !flex-col !gap-0 !whitespace-normal !break-word !border !border-[#8aaa21] !bg-black hover:!bg-[#8aaa21] hover:!text-black'
                    mode='dark'
                    disabled={submitting}
                    onClick={handleFinishSession}
                >
                    <p>Finish</p>
                    <p>Session</p>
                </CustomButton>
            </div>
            <Modal
                open={finishSessionReminderModalVisible}
                width={500}
                onOk={handleFirstFinishSession}
                onCancel={() => setFinishSessionReminderModalVisible(false)}
                okText='OK'
                cancelButtonProps={{ style: { display: 'none' } }}
                centered
            >
                <p className='text-center text-base'>
                    Please remember to upload completed tasks at the end of each working day in the&nbsp;
                    <span className='font-semibold'>Task Management</span>
                    &nbsp; page to stay eligible for rewards. You may only upload ONCE per day.
                </p>
            </Modal>
            <Modal
                open={reminderModalVisible}
                width={500}
                onOk={handleFirstConfirm}
                onCancel={() => setReminderModalVisible(false)}
                okText='OK'
                cancelButtonProps={{ style: { display: 'none' } }}
                centered
            >
                <p className='text-center text-base'>
                    Please remember to upload completed tasks at the end of each working day in the&nbsp;
                    <span className='font-semibold'>Task Management</span>
                    &nbsp; page to stay eligible for rewards. You may only upload ONCE per day.
                </p>
            </Modal>
            <Modal
                width={360}
                open={notCompleteModalVisible}
                onOk={() => setNotCompleteModalVisible(false)}
                onCancel={() => setNotCompleteModalVisible(false)}
                okText='OK'
                centered
                cancelButtonProps={{ style: { display: 'none' } }}
            >
                <p className='text-center text-base'>
                    {isReviewPage
                        ? 'Pending masks require review. Please complete all reviews before submission.'
                        : 'Pending masks require annotate. Please complete all annotations before submission.'}
                </p>
            </Modal>
            <Modal
                width={500}
                open={tutorialDoneVisible}
                onOk={() => {
                    setTutorialDoneVisible(false);
                    if (redirectParam) {
                        const path = isReviewPage ? 'data-review' : 'manual-labeling';
                        window.open(`${window.location.origin}/workspace/${path}/${redirectParam}`, '_self');
                    } else {
                        window.open(window.location.origin, '_self');
                    }
                }}
                onCancel={() => setTutorialDoneVisible(false)}
                okText={redirectParam ? 'CONTINUE' : 'TO HOME'}
                centered
                cancelButtonProps={{ style: { display: 'none' } }}
            >
                <p className='text-center text-base'>
                    {isReviewPage
                        ? 'Congratulations on finishing the review tutorial! You now have the tools to critically evaluate and provide constructive feedback.'
                        : "Great job completing the annotation tutorial! You've learned how to effectively annotate data, which is a crucial step in many projects."}
                </p>
            </Modal>
            <Modal
                title='Security Check'
                open={captchaModalVisible}
                onCancel={handleCaptchaCancel}
                footer={null}
                centered
                width={400}
                className='captcha-modal'
            >
                <div className='flex flex-col items-center gap-4'>
                    {captchaData && (
                        <div className='flex flex-col items-center gap-3'>
                            <div className='relative'>
                                <img
                                    src={captchaData.image}
                                    alt='Captcha'
                                    className='border border-gray-300 rounded'
                                    style={{ maxWidth: '200px', height: 'auto' }}
                                />
                            </div>
                            <div className='flex items-center gap-2 w-full'>
                                <Input
                                    placeholder='Enter captcha code'
                                    value={captchaInput}
                                    onChange={(e) => setCaptchaInput(e.target.value)}
                                    onPressEnter={handleCaptchaVerify}
                                    disabled={captchaLoading}
                                    className='flex-1'
                                />
                                <Button
                                    type='link'
                                    onClick={handleCaptchaRefresh}
                                    loading={captchaLoading}
                                    className='text-green-500 hover:text-green-600'
                                >
                                    Refresh
                                </Button>
                            </div>
                            <Button
                                type='primary'
                                onClick={handleCaptchaVerify}
                                loading={captchaLoading}
                                disabled={!captchaInput.trim()}
                                className='w-full bg-green-600 hover:bg-green-700 border-green-600'
                            >
                                Verify
                            </Button>
                        </div>
                    )}
                </div>
            </Modal>
        </Layout.Sider>
    );
}

export default connect(mapStateToProps, mapDispatchToProps)(React.memo(ObjectsSideBar));

// Copyright (C) 2020-2022 Intel Corporation
// Copyright (C) CVAT.ai Corporation
//
// SPDX-License-Identifier: MIT

@import 'base';

.attribute-annotation-workspace.ant-layout {
    height: 100%;
}

.attribute-annotation-sidebar:not(.ant-layout-sider-collapsed) {
    background: $background-color-2;
    padding: $grid-unit-size;

    > .ant-layout-sider-children {
        display: flex;
        flex-direction: column;
    }
}

.cvat-attribute-annotation-sidebar-object-switcher,
.cvat-attribute-annotation-sidebar-attribute-switcher {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 18px;
    margin-top: $grid-unit-size;

    > span {
        max-width: 60%;
        text-overflow: ellipsis;
        overflow: hidden;
    }

    > button > span[role='img'] {
        color: $objects-bar-icons-color;
    }
}

.cvat-attribute-annotation-sidebar-basics-editor {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    margin: $grid-unit-size 0;
}

.attribute-annotations-sidebar-not-found-wrapper {
    margin-top: $grid-unit-size * 3;
    text-align: center;
    flex-grow: 10;
}

.attribute-annotations-sidebar-attribute-editor {
    flex-grow: 10;
    overflow-y: auto;
}

.attribute-annotation-sidebar-attr-list-wrapper {
    margin: $grid-unit-size 0 $grid-unit-size $grid-unit-size;
}

.attribute-annotation-sidebar-attr-elem-wrapper {
    width: 60%;
}

.attribute-annotation-sidebar-number-list {
    display: flex;
    justify-content: space-around;
}

.cvat-attribute-annotation-sidebar-attr-editor {
    display: flex;
    align-items: center;
    justify-content: space-around;
}

.cvat-sidebar-collapse-button-spacer {
    height: $grid-unit-size * 4;
}

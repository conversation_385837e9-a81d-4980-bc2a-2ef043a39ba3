// Copyright (C) CVAT.ai Corporation
//
// SPDX-License-Identifier: MIT

@import 'base';

.cvat-analytics-inner {
    background: $background-color-1;
    min-height: $grid-unit-size * 95;
    padding: $grid-unit-size * 4;
    padding-bottom: $grid-unit-size;
    padding-top: 0;
    border-radius: $border-radius-base;

    .ant-tabs {
        height: 100%;
    }
}

.cvat-analytics-overview {
    >.ant-row {
        margin-top: $grid-unit-size;
    }
}

.cvat-empty-performance-analytics-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.cvat-analytics-refresh-button {
    margin-right: $grid-unit-size;
}

.cvat-analytics-card-title {
    font-size: 16px;
}

.cvat-analytics-card-value {
    font-size: 28px;
}

.cvat-analytics-tooltip {
    margin-left: $grid-unit-size;
}

.cvat-analytics-tooltip-inner {
    span {
        display: block;
        color: white;
    }
}

.cvat-analytics-settings-tooltip-inner {
    @extend .cvat-analytics-tooltip-inner;

    div:not(:last-child) {
        margin-bottom: $grid-unit-size * 2;
    }
}

.cvat-analytics-tooltip-conflicts-inner {
    @extend .cvat-analytics-tooltip-inner;

    min-width: $grid-unit-size * 47;
}

.cvat-analytics-page {
    height: 100%;
}

.cvat-analytics-wrapper {
    overflow-y: auto;
    width: 100%;
    height: 100%;
}
